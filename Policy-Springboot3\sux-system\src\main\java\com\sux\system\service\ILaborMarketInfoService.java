package com.sux.system.service;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sux.system.domain.LaborMarketInfo;

/**
 * 零工市场基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ILaborMarketInfoService extends IService<LaborMarketInfo>
{
    /**
     * 查询零工市场基础信息列表
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoList(LaborMarketInfo laborMarketInfo);

    /**
     * 查询零工市场基础信息
     * 
     * @param marketId 零工市场基础信息主键
     * @return 零工市场基础信息
     */
    public LaborMarketInfo selectLaborMarketInfoByMarketId(Long marketId);

    /**
     * 新增零工市场基础信息
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 结果
     */
    public int insertLaborMarketInfo(LaborMarketInfo laborMarketInfo);

    /**
     * 修改零工市场基础信息
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 结果
     */
    public int updateLaborMarketInfo(LaborMarketInfo laborMarketInfo);

    /**
     * 批量删除零工市场基础信息
     * 
     * @param marketIds 需要删除的零工市场基础信息主键集合
     * @return 结果
     */
    public int deleteLaborMarketInfoByMarketIds(Long[] marketIds);

    /**
     * 删除零工市场基础信息信息
     * 
     * @param marketId 零工市场基础信息主键
     * @return 结果
     */
    public int deleteLaborMarketInfoByMarketId(Long marketId);

    /**
     * 查询推荐零工市场信息列表
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectFeaturedLaborMarketInfoList(LaborMarketInfo laborMarketInfo);

    /**
     * 查询活跃零工市场信息列表
     * 
     * @param laborMarketInfo 零工市场基础信息
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectActiveLaborMarketInfoList(LaborMarketInfo laborMarketInfo);

    /**
     * 根据市场类型查询零工市场信息列表
     * 
     * @param marketType 市场类型
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoByType(String marketType);

    /**
     * 根据区域代码查询零工市场信息列表
     * 
     * @param regionCode 区域代码
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoByRegion(String regionCode);

    /**
     * 根据服务类别查询零工市场信息列表
     * 
     * @param serviceCategory 服务类别
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoByServiceCategory(String serviceCategory);

    /**
     * 查询零工市场统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> selectLaborMarketInfoStatistics();

    /**
     * 根据关键词搜索零工市场信息
     * 
     * @param keyword 关键词
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoByKeyword(String keyword);

    /**
     * 更新零工市场浏览次数
     * 
     * @param marketId 市场ID
     * @return 结果
     */
    public int updateLaborMarketInfoViewCount(Long marketId);

    /**
     * 查询零工市场详细信息（包含关联信息）
     * 
     * @param marketId 市场ID
     * @return 零工市场基础信息
     */
    public LaborMarketInfo selectLaborMarketInfoDetailByMarketId(Long marketId);

    /**
     * 获取所有市场类型列表
     * 
     * @return 市场类型列表
     */
    public List<String> selectAllMarketTypes();

    /**
     * 获取所有区域列表
     * 
     * @return 区域列表
     */
    public List<Map<String, String>> selectAllRegions();

    /**
     * 获取所有服务类别列表
     * 
     * @return 服务类别列表
     */
    public List<String> selectAllServiceCategories();

    /**
     * 根据容量范围查询零工市场信息
     * 
     * @param minCapacity 最小容量
     * @param maxCapacity 最大容量
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoByCapacityRange(Integer minCapacity, Integer maxCapacity);

    /**
     * 根据费用范围查询零工市场信息
     * 
     * @param minFee 最小费用
     * @param maxFee 最大费用
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectLaborMarketInfoByFeeRange(java.math.BigDecimal minFee, java.math.BigDecimal maxFee);

    /**
     * 查询高需求零工市场（根据日均用工需求排序）
     * 
     * @param limit 限制数量
     * @return 零工市场基础信息集合
     */
    public List<LaborMarketInfo> selectHighDemandLaborMarketInfo(Integer limit);
}
