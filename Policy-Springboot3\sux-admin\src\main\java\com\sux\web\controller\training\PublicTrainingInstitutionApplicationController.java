package com.sux.web.controller.training;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.SecurityUtils;
import com.sux.system.domain.TrainingInstitutionApplication;
import com.sux.system.domain.TrainingOrder;
import com.sux.system.service.ITrainingInstitutionApplicationService;
import com.sux.system.service.ITrainingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 培训机构申请公开接口Controller（无需登录）
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/public/training/institution/application")
public class PublicTrainingInstitutionApplicationController extends BaseController
{
    @Autowired
    private ITrainingInstitutionApplicationService trainingInstitutionApplicationService;
    
    @Autowired
    private ITrainingOrderService trainingOrderService;

    /**
     * 提交培训机构申请（公开接口，无需登录）
     */
    @Log(title = "培训机构申请", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    public AjaxResult submitApplication(@Validated @RequestBody TrainingInstitutionApplication trainingInstitutionApplication) {
        // 验证培训订单是否存在且可申请
        TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(trainingInstitutionApplication.getOrderId());
        if (order == null) {
            return error("培训订单不存在");
        }

        if (!"1".equals(order.getOrderStatus())) {
            return error("培训订单未发布或已下线，无法申请");
        }

        // 检查申请截止时间
        if (order.getRegistrationDeadline() != null && new Date().after(order.getRegistrationDeadline())) {
            return error("申请已截止");
        }

        // 检查申请唯一性
        if (!trainingInstitutionApplicationService.checkApplicationUnique(trainingInstitutionApplication)) {
            return error("您的机构已申请此培训，请勿重复申请");
        }

        // 提交申请
        int result = trainingInstitutionApplicationService.submitApplication(trainingInstitutionApplication);
        if (result > 0) {
            return success("申请提交成功，请等待审核");
        } else {
            return error("申请提交失败，请稍后重试");
        }
    }

    /**
     * 更新培训机构申请（公开接口，用于重新申请）
     */
    @Log(title = "培训机构申请", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult updateApplication(@Validated @RequestBody TrainingInstitutionApplication trainingInstitutionApplication) {
        // 验证申请是否存在
        TrainingInstitutionApplication existingApplication = trainingInstitutionApplicationService.selectTrainingInstitutionApplicationByApplicationId(trainingInstitutionApplication.getApplicationId());
        if (existingApplication == null) {
            return error("申请记录不存在");
        }

        // 验证培训订单是否存在且可申请
        TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(trainingInstitutionApplication.getOrderId());
        if (order == null) {
            return error("培训订单不存在");
        }

        if (!"1".equals(order.getOrderStatus())) {
            return error("培训订单未发布或已下线，无法申请");
        }

        // 检查申请截止时间
        if (order.getRegistrationDeadline() != null && new Date().after(order.getRegistrationDeadline())) {
            return error("申请已截止");
        }

        // 重置申请状态为待审核
        trainingInstitutionApplication.setApplicationStatus("0");
        trainingInstitutionApplication.setApplicationTime(new Date());
        trainingInstitutionApplication.setReviewTime(null);
        trainingInstitutionApplication.setReviewer(null);
        trainingInstitutionApplication.setReviewComment(null);

        // 更新申请
        int result = trainingInstitutionApplicationService.updateTrainingInstitutionApplication(trainingInstitutionApplication);
        if (result > 0) {
            return success("重新申请提交成功，请等待审核");
        } else {
            return error("重新申请提交失败，请稍后重试");
        }
    }

    /**
     * 检查机构申请状态（公开接口）
     */
    @GetMapping("/check-status")
    public AjaxResult checkApplicationStatus(@RequestParam Long orderId,
                                           @RequestParam(required = false) String institutionName,
                                           @RequestParam(required = false) String contactPhone,
                                           @RequestParam(required = false) Long userId) {
        TrainingInstitutionApplication application = null;
        
        // 优先通过用户ID查询
        if (userId != null) {
            application = trainingInstitutionApplicationService.checkInstitutionApplication(orderId, userId);
        }
        
        // 如果没有找到，通过机构名称查询
        if (application == null && institutionName != null && !institutionName.trim().isEmpty()) {
            application = trainingInstitutionApplicationService.checkInstitutionNameApplication(orderId, institutionName);
        }
        
        // 如果还没有找到，通过联系电话查询
        if (application == null && contactPhone != null && !contactPhone.trim().isEmpty()) {
            application = trainingInstitutionApplicationService.checkPhoneApplication(orderId, contactPhone);
        }
        
        if (application != null) {
            return success(application);
        } else {
            return success(null);
        }
    }

    /**
     * 获取当前登录用户的机构申请状态
     */
    @GetMapping("/my-status/{orderId}")
    public AjaxResult getMyApplicationStatus(@PathVariable Long orderId) {
        try {
            Long userId = SecurityUtils.getUserId();
            TrainingInstitutionApplication application = trainingInstitutionApplicationService.checkInstitutionApplication(orderId, userId);
            return success(application);
        } catch (Exception e) {
            return error("请先登录");
        }
    }

    /**
     * 获取当前登录用户的所有机构申请记录（需要登录）
     */
    @GetMapping("/my-applications")
    public AjaxResult getMyApplications() {
        try {
            Long userId = SecurityUtils.getUserId();
            List<TrainingInstitutionApplication> list = trainingInstitutionApplicationService.getMyApplications(userId);
            return success(list);
        } catch (Exception e) {
            return error("请先登录");
        }
    }

    /**
     * 取消我的机构申请
     */
    @Log(title = "取消培训机构申请", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{applicationId}")
    public AjaxResult cancelMyApplication(@PathVariable Long applicationId) {
        try {
            Long userId = SecurityUtils.getUserId();
            
            // 验证申请是否属于当前用户
            TrainingInstitutionApplication application = trainingInstitutionApplicationService.selectTrainingInstitutionApplicationByApplicationId(applicationId);
            if (application == null) {
                return error("申请记录不存在");
            }
            
            if (!userId.equals(application.getUserId())) {
                return error("无权操作此申请记录");
            }
            
            if (!"0".equals(application.getApplicationStatus())) {
                return error("只能取消待审核状态的申请");
            }
            
            int result = trainingInstitutionApplicationService.cancelApplication(applicationId);
            if (result > 0) {
                return success("取消申请成功");
            } else {
                return error("取消申请失败");
            }
        } catch (Exception e) {
            return error("请先登录");
        }
    }

    /**
     * 获取培训订单的机构申请统计信息（公开接口）
     */
    @GetMapping("/statistics/{orderId}")
    public AjaxResult getApplicationStatistics(@PathVariable Long orderId) {
        int totalCount = trainingInstitutionApplicationService.countApplicationsByOrderId(orderId);
        int approvedCount = trainingInstitutionApplicationService.countApprovedApplicationsByOrderId(orderId);
        
        return success(new Object() {
            public final int totalApplications = totalCount;
            public final int approvedApplications = approvedCount;
            public final int pendingApplications = totalCount - approvedCount;
        });
    }

    /**
     * 获取某个培训订单的已通过机构申请列表（公开接口，仅显示基本信息）
     */
    @GetMapping("/approved/{orderId}")
    public AjaxResult getApprovedApplications(@PathVariable Long orderId) {
        List<TrainingInstitutionApplication> allApplications = trainingInstitutionApplicationService.getApplicationsByOrderId(orderId);
        
        // 过滤出已通过的申请，并只返回基本信息
        List<TrainingInstitutionApplication> approvedApplications = allApplications.stream()
                .filter(app -> "1".equals(app.getApplicationStatus()))
                .peek(app -> {
                    // 清除敏感信息，只保留基本展示信息
                    app.setContactPhone(null);
                    app.setContactEmail(null);
                    app.setInstitutionCode(null);
                    app.setLegalPerson(null);
                    app.setBusinessScope(null);
                    app.setQualificationFiles(null);
                    app.setTrainingPlanFile(null);
                    app.setTeacherCertFiles(null);
                    app.setFacilityFiles(null);
                    app.setOtherFiles(null);
                    app.setApplicationNote(null);
                    app.setReviewComment(null);
                })
                .toList();
        
        return success(approvedApplications);
    }
}
