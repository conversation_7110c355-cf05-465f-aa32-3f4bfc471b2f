package com.sux.web.controller.place;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.enums.BusinessType;
import com.sux.system.domain.EmploymentInfo;
import com.sux.system.service.IEmploymentInfoService;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.common.core.page.TableDataInfo;

/**
 * 用工信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/place/employment")
public class EmploymentInfoController extends BaseController
{
    @Autowired
    private IEmploymentInfoService employmentInfoService;

    /**
     * 查询用工信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EmploymentInfo employmentInfo)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoList(employmentInfo);
        return getDataTable(list);
    }

    /**
     * 查询已发布的用工信息列表
     */
    @GetMapping("/published")
    public TableDataInfo publishedList(EmploymentInfo employmentInfo)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectPublishedEmploymentInfoList(employmentInfo);
        return getDataTable(list);
    }

    /**
     * 查询推荐用工信息列表
     */
    @GetMapping("/featured")
    public TableDataInfo featuredList(EmploymentInfo employmentInfo)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectFeaturedEmploymentInfoList(employmentInfo);
        return getDataTable(list);
    }

    /**
     * 查询我发布的用工信息列表
     */
    @GetMapping("/my")
    public TableDataInfo myList(EmploymentInfo employmentInfo)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectMyEmploymentInfoList(employmentInfo);
        return getDataTable(list);
    }

    /**
     * 导出用工信息列表
     */
    @Log(title = "用工信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmploymentInfo employmentInfo)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoList(employmentInfo);
        ExcelUtil<EmploymentInfo> util = new ExcelUtil<EmploymentInfo>(EmploymentInfo.class);
        util.exportExcel(response, list, "用工信息数据");
    }

    /**
     * 获取用工信息详细信息
     */
    @GetMapping(value = "/{employmentId}")
    public AjaxResult getInfo(@PathVariable("employmentId") Long employmentId)
    {
        return success(employmentInfoService.selectEmploymentInfoByEmploymentId(employmentId));
    }

    /**
     * 获取用工信息详细信息（包含关联信息）
     */
    @GetMapping(value = "/detail/{employmentId}")
    public AjaxResult getDetail(@PathVariable("employmentId") Long employmentId)
    {
        EmploymentInfo employmentInfo = employmentInfoService.selectEmploymentInfoDetailByEmploymentId(employmentId);
        if (employmentInfo != null) {
            // 更新浏览次数
            employmentInfoService.updateEmploymentInfoViewCount(employmentId);
        }
        return success(employmentInfo);
    }

    /**
     * 新增用工信息
     */
    @Log(title = "用工信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmploymentInfo employmentInfo)
    {
        return toAjax(employmentInfoService.insertEmploymentInfo(employmentInfo));
    }

    /**
     * 修改用工信息
     */
    @Log(title = "用工信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmploymentInfo employmentInfo)
    {
        return toAjax(employmentInfoService.updateEmploymentInfo(employmentInfo));
    }

    /**
     * 删除用工信息
     */
    @Log(title = "用工信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{employmentIds}")
    public AjaxResult remove(@PathVariable Long[] employmentIds)
    {
        return toAjax(employmentInfoService.deleteEmploymentInfoByEmploymentIds(employmentIds));
    }

    /**
     * 根据用工类型查询用工信息列表
     */
    @GetMapping("/type/{employmentType}")
    public AjaxResult getByType(@PathVariable String employmentType)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByType(employmentType);
        return success(list);
    }

    /**
     * 根据工作类别查询用工信息列表
     */
    @GetMapping("/category/{workCategory}")
    public AjaxResult getByCategory(@PathVariable String workCategory)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByCategory(workCategory);
        return success(list);
    }

    /**
     * 根据区域代码查询用工信息列表
     */
    @GetMapping("/region/{regionCode}")
    public AjaxResult getByRegion(@PathVariable String regionCode)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByRegion(regionCode);
        return success(list);
    }

    /**
     * 根据薪资类型查询用工信息列表
     */
    @GetMapping("/salary-type/{salaryType}")
    public AjaxResult getBySalaryType(@PathVariable String salaryType)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoBySalaryType(salaryType);
        return success(list);
    }

    /**
     * 根据薪资范围查询用工信息列表
     */
    @GetMapping("/salary-range")
    public AjaxResult getBySalaryRange(@RequestParam(required = false) java.math.BigDecimal minSalary,
                                      @RequestParam(required = false) java.math.BigDecimal maxSalary)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoBySalaryRange(minSalary, maxSalary);
        return success(list);
    }

    /**
     * 根据紧急程度查询用工信息列表
     */
    @GetMapping("/urgency/{urgencyLevel}")
    public AjaxResult getByUrgency(@PathVariable String urgencyLevel)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByUrgency(urgencyLevel);
        return success(list);
    }

    /**
     * 查询用工信息统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> statistics = employmentInfoService.selectEmploymentInfoStatistics();
        return success(statistics);
    }

    /**
     * 根据关键词搜索用工信息
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam String keyword)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 查询即将到期的用工信息
     */
    @GetMapping("/expiring")
    public AjaxResult getExpiringSoon(@RequestParam(defaultValue = "7") Integer days)
    {
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoExpiringSoon(days);
        return success(list);
    }

    /**
     * 查询相似的用工信息列表
     */
    @GetMapping("/{employmentId}/similar")
    public AjaxResult getSimilar(@PathVariable Long employmentId, @RequestParam(defaultValue = "5") Integer limit)
    {
        EmploymentInfo employmentInfo = employmentInfoService.selectEmploymentInfoByEmploymentId(employmentId);
        if (employmentInfo == null) {
            return error("用工信息不存在");
        }
        List<EmploymentInfo> list = employmentInfoService.selectSimilarEmploymentInfoList(employmentInfo, limit);
        return success(list);
    }

    /**
     * 更新用工信息申请次数
     */
    @PostMapping("/{employmentId}/apply")
    public AjaxResult updateApplicationCount(@PathVariable Long employmentId)
    {
        return toAjax(employmentInfoService.updateEmploymentInfoApplicationCount(employmentId));
    }

    /**
     * 获取所有用工类型列表
     */
    @GetMapping("/types")
    public AjaxResult getAllEmploymentTypes()
    {
        List<String> types = employmentInfoService.selectAllEmploymentTypes();
        return success(types);
    }

    /**
     * 获取所有工作类别列表
     */
    @GetMapping("/categories")
    public AjaxResult getAllWorkCategories()
    {
        List<String> categories = employmentInfoService.selectAllWorkCategories();
        return success(categories);
    }

    /**
     * 获取所有薪资类型列表
     */
    @GetMapping("/salary-types")
    public AjaxResult getAllSalaryTypes()
    {
        List<String> salaryTypes = employmentInfoService.selectAllSalaryTypes();
        return success(salaryTypes);
    }

    /**
     * 获取所有区域列表
     */
    @GetMapping("/regions")
    public AjaxResult getAllRegions()
    {
        List<Map<String, String>> regions = employmentInfoService.selectAllRegions();
        return success(regions);
    }

    /**
     * 获取所有学历要求列表
     */
    @GetMapping("/educations")
    public AjaxResult getAllEducationRequirements()
    {
        List<String> educations = employmentInfoService.selectAllEducationRequirements();
        return success(educations);
    }

    /**
     * 根据核心字段搜索用工信息
     */
    @GetMapping("/core-search")
    public TableDataInfo coreSearch(@RequestParam(required = false) String employmentType,
                                   @RequestParam(required = false) String workCategory,
                                   @RequestParam(required = false) String salaryType,
                                   @RequestParam(required = false) String regionCode,
                                   @RequestParam(required = false) String keyword)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByCoreFields(
            employmentType, workCategory, salaryType, regionCode, keyword);
        return getDataTable(list);
    }
}
