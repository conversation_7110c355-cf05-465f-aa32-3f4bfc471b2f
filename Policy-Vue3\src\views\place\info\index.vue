<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="场地名称" prop="placeName">
        <el-input
          v-model="queryParams.placeName"
          placeholder="请输入场地名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="场地类型" prop="placeType">
        <el-select v-model="queryParams.placeType" placeholder="请选择场地类型" clearable>
          <el-option
            v-for="type in placeTypeOptions"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场地等级" prop="placeLevel">
        <el-select v-model="queryParams.placeLevel" placeholder="请选择场地等级" clearable>
          <el-option
            v-for="level in placeLevelOptions"
            :key="level"
            :label="level"
            :value="level"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="区域" prop="regionCode">
        <el-select v-model="queryParams.regionCode" placeholder="请选择区域" clearable>
          <el-option
            v-for="region in regionOptions"
            :key="region.regionCode"
            :label="region.regionName"
            :value="region.regionCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['place:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['place:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['place:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['place:info:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="placeInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="场地ID" align="center" prop="placeId" />
      <el-table-column label="场地名称" align="center" prop="placeName" :show-overflow-tooltip="true" />
      <el-table-column label="场地编码" align="center" prop="placeCode" />
      <el-table-column label="场地类型" align="center" prop="placeType" />
      <el-table-column label="场地等级" align="center" prop="placeLevel" />
      <el-table-column label="区域" align="center" prop="regionName" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="入驻企业" align="center" prop="companyCount" />
      <el-table-column label="可用工位" align="center" prop="availablePositions" />
      <el-table-column label="已占工位" align="center" prop="occupiedPositions" />
      <el-table-column label="是否推荐" align="center" prop="isFeatured">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isFeatured"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="viewCount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['place:info:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['place:info:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['place:info:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改场地信息对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="placeInfoRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="场地名称" prop="placeName">
              <el-input v-model="form.placeName" placeholder="请输入场地名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场地编码" prop="placeCode">
              <el-input v-model="form.placeCode" placeholder="请输入场地编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="场地类型" prop="placeType">
              <el-select v-model="form.placeType" placeholder="请选择场地类型">
                <el-option label="创业园区" value="创业园区" />
                <el-option label="孵化器" value="孵化器" />
                <el-option label="众创空间" value="众创空间" />
                <el-option label="产业园" value="产业园" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场地等级" prop="placeLevel">
              <el-select v-model="form.placeLevel" placeholder="请选择场地等级">
                <el-option label="国家级" value="国家级" />
                <el-option label="省级" value="省级" />
                <el-option label="市级" value="市级" />
                <el-option label="区级" value="区级" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="场地面积" prop="placeArea">
              <el-input v-model="form.placeArea" placeholder="请输入场地面积（平方米）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可使用面积" prop="usableArea">
              <el-input v-model="form.usableArea" placeholder="请输入可使用面积（平方米）" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="区域代码" prop="regionCode">
              <el-input v-model="form.regionCode" placeholder="请输入区域代码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域名称" prop="regionName">
              <el-input v-model="form.regionName" placeholder="请输入区域名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="入驻企业数" prop="companyCount">
              <el-input-number v-model="form.companyCount" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="可提供工位" prop="availablePositions">
              <el-input-number v-model="form.availablePositions" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已占用工位" prop="occupiedPositions">
              <el-input-number v-model="form.occupiedPositions" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最低租金" prop="rentPriceMin">
              <el-input v-model="form.rentPriceMin" placeholder="元/月/平方米" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最高租金" prop="rentPriceMax">
              <el-input v-model="form.rentPriceMax" placeholder="元/月/平方米" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="运营模式" prop="operationMode">
          <el-select v-model="form.operationMode" placeholder="请选择运营模式">
            <el-option label="自营" value="自营" />
            <el-option label="委托运营" value="委托运营" />
            <el-option label="合作运营" value="合作运营" />
          </el-select>
        </el-form-item>
        <el-form-item label="行业方向" prop="industryDirection">
          <el-input v-model="form.industryDirection" placeholder="多个用逗号分隔" />
        </el-form-item>
        <el-form-item label="场地描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入场地详细描述" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="是否推荐" prop="isFeatured">
              <el-radio-group v-model="form.isFeatured">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开放入驻" prop="isOpenSettle">
              <el-radio-group v-model="form.isOpenSettle">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 场地详情对话框 -->
    <el-dialog title="场地详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="场地名称">{{ detailForm.placeName }}</el-descriptions-item>
        <el-descriptions-item label="场地编码">{{ detailForm.placeCode }}</el-descriptions-item>
        <el-descriptions-item label="场地类型">{{ detailForm.placeType }}</el-descriptions-item>
        <el-descriptions-item label="场地等级">{{ detailForm.placeLevel }}</el-descriptions-item>
        <el-descriptions-item label="场地面积">{{ detailForm.placeArea }} 平方米</el-descriptions-item>
        <el-descriptions-item label="可使用面积">{{ detailForm.usableArea }} 平方米</el-descriptions-item>
        <el-descriptions-item label="详细地址" :span="2">{{ detailForm.address }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailForm.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱" :span="2">{{ detailForm.contactEmail }}</el-descriptions-item>
        <el-descriptions-item label="入驻企业数">{{ detailForm.companyCount }}</el-descriptions-item>
        <el-descriptions-item label="可提供工位">{{ detailForm.availablePositions }}</el-descriptions-item>
        <el-descriptions-item label="已占用工位">{{ detailForm.occupiedPositions }}</el-descriptions-item>
        <el-descriptions-item label="浏览次数">{{ detailForm.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="运营模式">{{ detailForm.operationMode }}</el-descriptions-item>
        <el-descriptions-item label="行业方向">{{ detailForm.industryDirection }}</el-descriptions-item>
        <el-descriptions-item label="场地描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="PlaceInfo">
import { listPlaceInfo, getPlaceInfo, delPlaceInfo, addPlaceInfo, updatePlaceInfo } from "@/api/place/info";

const { proxy } = getCurrentInstance();
const { sys_normal_disable, sys_yes_no } = proxy.useDict('sys_normal_disable', 'sys_yes_no');

const placeInfoList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 选项数据
const placeTypeOptions = ref([]);
const placeLevelOptions = ref([]);
const regionOptions = ref([]);

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    placeName: null,
    placeCode: null,
    placeType: null,
    placeLevel: null,
    regionCode: null,
    status: null,
  },
  rules: {
    placeName: [
      { required: true, message: "场地名称不能为空", trigger: "blur" }
    ],
    placeType: [
      { required: true, message: "场地类型不能为空", trigger: "change" }
    ],
    address: [
      { required: true, message: "详细地址不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询场地信息列表 */
function getList() {
  loading.value = true;
  listPlaceInfo(queryParams.value).then(response => {
    placeInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    placeId: null,
    placeName: null,
    placeCode: null,
    placeType: null,
    placeLevel: null,
    placeArea: null,
    usableArea: null,
    address: null,
    regionCode: null,
    regionName: null,
    contactPerson: null,
    contactPhone: null,
    contactEmail: null,
    companyCount: 0,
    availablePositions: 0,
    occupiedPositions: 0,
    rentPriceMin: null,
    rentPriceMax: null,
    operationMode: null,
    industryDirection: null,
    description: null,
    status: "0",
    isFeatured: 0,
    isOpenSettle: 1,
    remark: null
  };
  proxy.resetForm("placeInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.placeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加场地信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _placeId = row.placeId || ids.value
  getPlaceInfo(_placeId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改场地信息";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _placeId = row.placeId;
  getPlaceInfo(_placeId).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["placeInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.placeId != null) {
        updatePlaceInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPlaceInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _placeIds = row.placeId || ids.value;
  proxy.$modal.confirm('是否确认删除场地信息编号为"' + _placeIds + '"的数据项？').then(function() {
    return delPlaceInfo(_placeIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('place/info/export', {
    ...queryParams.value
  }, `place_info_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  // 加载选项数据
  // 这里可以调用API获取选项数据
  placeTypeOptions.value = ['创业园区', '孵化器', '众创空间', '产业园'];
  placeLevelOptions.value = ['国家级', '省级', '市级', '区级'];
  regionOptions.value = [
    { regionCode: '370202', regionName: '市南区' },
    { regionCode: '370203', regionName: '市北区' },
    { regionCode: '370212', regionName: '崂山区' }
  ];
});
</script>
