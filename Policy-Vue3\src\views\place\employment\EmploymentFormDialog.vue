<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    :width="formOption.dialogWidth || '1200px'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="formOption.labelWidth || '120px'"
      :size="formOption.size || 'default'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用工标题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入用工标题"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用工类型" prop="employmentType">
            <el-select
              v-model="formData.employmentType"
              placeholder="请选择用工类型"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="日结" value="日结" />
              <el-option label="周结" value="周结" />
              <el-option label="月结" value="月结" />
              <el-option label="计件" value="计件" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="工作类别" prop="workCategory">
            <el-select
              v-model="formData.workCategory"
              placeholder="请选择工作类别"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="服务员" value="服务员" />
              <el-option label="保洁" value="保洁" />
              <el-option label="搬运工" value="搬运工" />
              <el-option label="销售" value="销售" />
              <el-option label="厨师助手" value="厨师助手" />
              <el-option label="快递员" value="快递员" />
              <el-option label="保安" value="保安" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作地点" prop="workLocation">
            <el-input
              v-model="formData.workLocation"
              placeholder="请输入工作地点"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="薪资类型" prop="salaryType">
            <el-select
              v-model="formData.salaryType"
              placeholder="请选择薪资类型"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="小时薪" value="hourly" />
              <el-option label="日薪" value="daily" />
              <el-option label="月薪" value="monthly" />
              <el-option label="计件" value="piece" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最低薪资" prop="salaryMin">
            <el-input-number
              v-model="formData.salaryMin"
              placeholder="请输入最低薪资"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最高薪资" prop="salaryMax">
            <el-input-number
              v-model="formData.salaryMax"
              placeholder="请输入最高薪资"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="需要人数" prop="positionsNeeded">
            <el-input-number
              v-model="formData.positionsNeeded"
              placeholder="请输入需要人数"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="已招人数" prop="positionsFilled">
            <el-input-number
              v-model="formData.positionsFilled"
              placeholder="请输入已招人数"
              :disabled="mode === 'view'"
              style="width: 100%"
              :min="0"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="紧急程度" prop="urgencyLevel">
            <el-select
              v-model="formData.urgencyLevel"
              placeholder="请选择紧急程度"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="紧急" value="urgent" />
              <el-option label="高" value="high" />
              <el-option label="普通" value="normal" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companyName">
            <el-input
              v-model="formData.companyName"
              placeholder="请输入公司名称"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input
              v-model="formData.contactPerson"
              placeholder="请输入联系人"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
              v-model="formData.contactPhone"
              placeholder="请输入联系电话"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择状态"
              :disabled="mode === 'view'"
              style="width: 100%"
            >
              <el-option label="草稿" value="draft" />
              <el-option label="已发布" value="published" />
              <el-option label="已暂停" value="paused" />
              <el-option label="已关闭" value="closed" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="工作描述" prop="workDescription">
            <el-input
              v-model="formData.workDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入工作描述"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="福利待遇" prop="welfareBenefits">
            <el-input
              v-model="formData.welfareBenefits"
              type="textarea"
              :rows="3"
              placeholder="请输入福利待遇"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer v-if="mode !== 'view'">
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'cancel'])

const dialogVisible = ref(false)
const mode = ref('add') // add, edit, view
const formRef = ref(null)

const formData = reactive({
  employmentId: null,
  title: '',
  employmentType: '',
  workCategory: '',
  workLocation: '',
  salaryType: '',
  salaryMin: null,
  salaryMax: null,
  positionsNeeded: 1,
  positionsFilled: 0,
  companyName: '',
  contactPerson: '',
  contactPhone: '',
  urgencyLevel: 'normal',
  status: 'draft',
  workDescription: '',
  welfareBenefits: ''
})

const formRules = {
  title: [
    { required: true, message: '用工标题不能为空', trigger: 'blur' }
  ],
  employmentType: [
    { required: true, message: '用工类型不能为空', trigger: 'change' }
  ],
  workCategory: [
    { required: true, message: '工作类别不能为空', trigger: 'change' }
  ],
  workLocation: [
    { required: true, message: '工作地点不能为空', trigger: 'blur' }
  ],
  salaryType: [
    { required: true, message: '薪资类型不能为空', trigger: 'change' }
  ],
  positionsNeeded: [
    { required: true, message: '需要人数不能为空', trigger: 'blur' }
  ],
  companyName: [
    { required: true, message: '公司名称不能为空', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '联系人不能为空', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' }
  ]
}

const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增用工信息',
    edit: '编辑用工信息',
    view: '查看用工信息'
  }
  return titleMap[mode.value] || '用工信息'
})

const openDialog = (dialogMode, data = {}) => {
  mode.value = dialogMode
  dialogVisible.value = true
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (data[key] !== undefined) {
      formData[key] = data[key]
    } else {
      // 设置默认值
      if (key === 'positionsNeeded') {
        formData[key] = 1
      } else if (key === 'positionsFilled') {
        formData[key] = 0
      } else if (key === 'urgencyLevel') {
        formData[key] = 'normal'
      } else if (key === 'status') {
        formData[key] = 'draft'
      } else {
        formData[key] = null
      }
    }
  })
  
  // 清除验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSubmit = () => {
  if (mode.value === 'view') return
  
  formRef.value.validate((valid) => {
    if (valid) {
      emit('submit', { ...formData }, mode.value)
      dialogVisible.value = false
    }
  })
}

const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

defineExpose({
  openDialog
})
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
