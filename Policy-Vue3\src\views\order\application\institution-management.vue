<template>
  <div class="institution-application-management app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="applicationList" :loading="tableLoading"
      :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
      operationWidth="300" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
      </template>

      <!-- 申请状态列插槽 -->
      <template #applicationStatus="{ row }">
        <el-tag :type="getStatusTagType(row.applicationStatus)">
          {{ getStatusText(row.applicationStatus) }}
        </el-tag>
      </template>

      <!-- 申请时间列插槽 -->
      <template #applicationTime="{ row }">
        {{ formatDateTime(row.applicationTime) }}
      </template>

      <!-- 审核时间列插槽 -->
      <template #reviewTime="{ row }">
        {{ formatDateTime(row.reviewTime) }}
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="primary" link @click="handleUpdate(row)"
            v-hasPermi="['training:institution:application:edit']">编辑</el-button>
          <el-button v-if="row.applicationStatus === '0'" type="success" link @click="handleReview(row, '1')"
            v-hasPermi="['training:institution:application:review']">通过</el-button>
          <el-button v-if="row.applicationStatus === '0'" type="warning" link @click="handleReview(row, '2')"
            v-hasPermi="['training:institution:application:review']">拒绝</el-button>
          <el-button v-if="['0', '1'].includes(row.applicationStatus)" type="info" link @click="handleCancel(row)"
            v-hasPermi="['training:institution:application:cancel']">取消</el-button>
          <el-button type="danger" link @click="handleDelete(row)"
            v-hasPermi="['training:institution:application:remove']">删除</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <InstitutionApplicationFormDialog ref="applicationFormDialogRef" :formFields="formFields" :formOption="formOption"
      @submit="handleFormSubmit" @cancel="handleFormCancel" />

    <!-- 审核弹窗 -->
    <el-dialog v-model="reviewDialog.visible" :title="reviewDialog.title" width="500px" append-to-body>
      <el-form ref="reviewFormRef" :model="reviewDialog.form" label-width="80px">
        <el-form-item label="审核状态">
          <el-radio-group v-model="reviewDialog.form.status">
            <el-radio value="1">通过</el-radio>
            <el-radio value="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input v-model="reviewDialog.form.reviewComment" type="textarea" :rows="4"
            placeholder="请输入审核意见"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reviewDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="submitReview">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量审核弹窗 -->
    <el-dialog v-model="batchReviewDialog.visible" :title="batchReviewDialog.title" width="500px" append-to-body>
      <el-form ref="batchReviewFormRef" :model="batchReviewDialog.form" label-width="80px">
        <el-form-item label="审核意见">
          <el-input v-model="batchReviewDialog.form.reviewComment" type="textarea" :rows="4"
            placeholder="请输入审核意见"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchReviewDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchReview">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="InstitutionApplicationManagement">
import { ref, reactive, onMounted, nextTick, getCurrentInstance, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { 
  listTrainingInstitutionApplication, 
  getTrainingInstitutionApplication, 
  delTrainingInstitutionApplication, 
  addTrainingInstitutionApplication, 
  updateTrainingInstitutionApplication, 
  reviewTrainingInstitutionApplication, 
  batchReviewTrainingInstitutionApplication, 
  cancelTrainingInstitutionApplication 
} from "@/api/training/institutionApplication"
import { createInstitutionApplicationTableOption } from "@/const/training/institutionApplication"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import InstitutionApplicationFormDialog from './InstitutionApplicationFormDialog.vue'

const { proxy } = getCurrentInstance()

const applicationList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const isInitializing = ref(true) // 添加初始化标志

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '1000px',
  dialogHeight: '80vh'
})
const tableListRef = ref(null)
const applicationFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

// 审核弹窗
const reviewDialog = ref({
  visible: false,
  title: '',
  form: {
    applicationId: null,
    status: '1',
    reviewComment: ''
  }
})

// 批量审核弹窗
const batchReviewDialog = ref({
  visible: false,
  title: '',
  form: {
    status: '1',
    reviewComment: ''
  }
})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderId: undefined,
    institutionName: undefined,
    contactPhone: undefined,
    applicationStatus: undefined,
    reviewer: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createInstitutionApplicationTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
  }
};

/** 查询培训机构申请列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listTrainingInstitutionApplication(params).then(res => {
    tableLoading.value = false
    loading.value = false
    applicationList.value = res.rows
    total.value = res.total

    // 数据加载完成后，设置初始化完成
    nextTick(() => {
      isInitializing.value = false
    })
  })
}

// 处理搜索
const handleSearch = (params) => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params };

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {};
  Object.assign(queryParams.value, otherParams);
  queryParams.value.pageNum = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    orderId: undefined,
    institutionName: undefined,
    contactPhone: undefined,
    applicationStatus: undefined,
    reviewer: undefined
  };
  searchParams.value = {};
  getList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  isInitializing.value = true;
  queryParams.value.pageNum = page;
  getList();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  isInitializing.value = true;
  queryParams.value.pageSize = size;
  queryParams.value.pageNum = 1;
  getList();
};

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.applicationId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  return proxy.parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}')
}

// 查看
const handleView = (row) => {
  applicationFormDialogRef.value?.openDialog('view', '查看培训机构申请', row)
}

// 编辑
const handleEdit = (row) => {
  const applicationId = row.applicationId
  getTrainingInstitutionApplication(applicationId).then(response => {
    applicationFormDialogRef.value?.openDialog('edit', '编辑培训机构申请', response.data)
  })
}

// 新增
const handleAddApplication = () => {
  const defaultData = {
    applicationStatus: "0"
  }
  applicationFormDialogRef.value?.openDialog('add', '新增培训机构申请', defaultData)
}

// 处理表单提交事件
const handleFormSubmit = async (payload) => {
  try {
    if (payload.type === 'add') {
      // 新增
      await addTrainingInstitutionApplication(payload.data)
      proxy.$modal.msgSuccess("添加成功")
    } else if (payload.type === 'edit') {
      // 编辑
      await updateTrainingInstitutionApplication(payload.data)
      proxy.$modal.msgSuccess("修改成功")
    }

    // 通知子组件提交成功
    applicationFormDialogRef.value?.onSubmitSuccess()
    getList()
  } catch (error) {
    // 通知子组件提交失败
    applicationFormDialogRef.value?.onSubmitError()
    console.error('提交失败:', error)
  }
}

// 处理表单取消事件
const handleFormCancel = () => {
  // 可以在这里添加取消逻辑
}

/** 新增按钮操作 */
function handleAdd() {
  handleAddApplication()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  if (row) {
    handleEdit(row)
  } else {
    // 批量编辑
    const applicationId = ids.value[0]
    const selectedRow = applicationList.value.find(item => item.applicationId === applicationId)
    if (selectedRow) {
      handleEdit(selectedRow)
    }
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const applicationIds = row.applicationId || ids.value
  proxy.$modal.confirm('是否确认删除培训机构申请编号为"' + applicationIds + '"的数据项？').then(function () {
    return delTrainingInstitutionApplication(applicationIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("training/institution/application/export", {
    ...queryParams.value,
  }, `institution_application_${new Date().getTime()}.xlsx`)
}

/** 审核按钮操作 */
function handleReview(row, status) {
  reviewDialog.value.visible = true
  reviewDialog.value.title = status === '1' ? '通过审核' : '拒绝审核'
  reviewDialog.value.form.applicationId = row.applicationId
  reviewDialog.value.form.status = status
  reviewDialog.value.form.reviewComment = ''
}

/** 批量审核按钮操作 */
function handleBatchReview(status) {
  const applicationIds = ids.value
  if (applicationIds.length === 0) {
    proxy.$modal.msgError("请选择要审核的申请记录")
    return
  }

  const action = status === '1' ? '通过' : '拒绝'
  batchReviewDialog.value.visible = true
  batchReviewDialog.value.title = `批量${action}审核`
  batchReviewDialog.value.form.status = status
  batchReviewDialog.value.form.reviewComment = ''
}

/** 提交审核 */
function submitReview() {
  const form = reviewDialog.value.form
  reviewTrainingInstitutionApplication(form.applicationId, form.status, form.reviewComment).then(() => {
    reviewDialog.value.visible = false
    getList()
    proxy.$modal.msgSuccess("审核成功")
  }).catch(() => { })
}

/** 提交批量审核 */
function submitBatchReview() {
  const form = batchReviewDialog.value.form
  const applicationIds = ids.value
  batchReviewTrainingInstitutionApplication(applicationIds, form.status, form.reviewComment).then(() => {
    batchReviewDialog.value.visible = false
    getList()
    proxy.$modal.msgSuccess("批量审核成功")
  }).catch(() => { })
}

/** 取消申请 */
function handleCancel(row) {
  proxy.$modal.confirm('确认要取消"' + row.institutionName + '"的申请吗？').then(function () {
    return cancelTrainingInstitutionApplication(row.applicationId)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("取消成功")
  }).catch(() => { })
}
</script>

<style lang="scss" scoped></style>
