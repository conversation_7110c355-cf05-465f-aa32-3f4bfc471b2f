package com.sux.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sux.system.domain.WorkerProfile;

import java.util.List;
import java.util.Map;

/**
 * 零工信息Service接口（核心匹配优化版）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IWorkerProfileService extends IService<WorkerProfile>
{
    /**
     * 查询零工信息列表
     * 
     * @param workerProfile 零工信息
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileList(WorkerProfile workerProfile);

    /**
     * 查询零工信息
     * 
     * @param workerId 零工信息主键
     * @return 零工信息
     */
    public WorkerProfile selectWorkerProfileByWorkerId(Long workerId);

    /**
     * 根据用户ID查询零工信息
     * 
     * @param userId 用户ID
     * @return 零工信息
     */
    public WorkerProfile selectWorkerProfileByUserId(Long userId);

    /**
     * 新增零工信息
     * 
     * @param workerProfile 零工信息
     * @return 结果
     */
    public int insertWorkerProfile(WorkerProfile workerProfile);

    /**
     * 修改零工信息
     * 
     * @param workerProfile 零工信息
     * @return 结果
     */
    public int updateWorkerProfile(WorkerProfile workerProfile);

    /**
     * 批量删除零工信息
     * 
     * @param workerIds 需要删除的零工信息主键集合
     * @return 结果
     */
    public int deleteWorkerProfileByWorkerIds(Long[] workerIds);

    /**
     * 删除零工信息信息
     * 
     * @param workerId 零工信息主键
     * @return 结果
     */
    public int deleteWorkerProfileByWorkerId(Long workerId);

    /**
     * 查询活跃的零工信息列表
     * 
     * @param workerProfile 零工信息
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectActiveWorkerProfileList(WorkerProfile workerProfile);

    /**
     * 查询已验证的零工信息列表
     * 
     * @param workerProfile 零工信息
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectVerifiedWorkerProfileList(WorkerProfile workerProfile);

    /**
     * 激活零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    public int activateWorkerProfile(Long workerId);

    /**
     * 停用零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    public int deactivateWorkerProfile(Long workerId);

    /**
     * 暂停零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    public int suspendWorkerProfile(Long workerId);

    /**
     * 禁用零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    public int banWorkerProfile(Long workerId);

    /**
     * 验证零工信息
     * 
     * @param workerId 零工信息ID
     * @return 结果
     */
    public int verifyWorkerProfile(Long workerId);

    /**
     * 更新零工最后活跃时间
     * 
     * @param workerId 零工ID
     * @return 结果
     */
    public int updateLastActiveTime(Long workerId);

    /**
     * 更新零工评分信息
     * 
     * @param workerId 零工ID
     * @param rating 新评分
     * @return 结果
     */
    public int updateWorkerRating(Long workerId, Double rating);

    /**
     * 更新零工完成工作统计
     * 
     * @param workerId 零工ID
     * @param isSuccess 是否成功完成
     * @return 结果
     */
    public int updateWorkerJobStats(Long workerId, Boolean isSuccess);

    /**
     * 查询高评分零工信息列表
     * 
     * @param minRating 最低评分
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectHighRatedWorkerProfileList(Double minRating, Integer limit);

    /**
     * 查询经验丰富的零工信息列表
     * 
     * @param minExperience 最少经验年数
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectExperiencedWorkerProfileList(Integer minExperience, Integer limit);

    /**
     * 根据工作类别统计零工数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectWorkerProfileCountByCategory();

    /**
     * 根据所在地统计零工数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectWorkerProfileCountByLocation();

    /**
     * 根据学历统计零工数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectWorkerProfileCountByEducation();

    /**
     * 根据经验年数统计零工数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectWorkerProfileCountByExperience();

    /**
     * 根据关键词搜索零工信息
     * 
     * @param keyword 关键词
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByKeyword(String keyword);

    /**
     * 查询推荐零工信息
     * 
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectRecommendedWorkerProfileList(Integer limit);

    /**
     * 查询新注册的零工信息
     * 
     * @param days 天数
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectNewWorkerProfileList(Integer days, Integer limit);

    /**
     * 查询即将可工作的零工信息
     * 
     * @param days 天数
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileAvailableSoon(Integer days);

    /**
     * 批量更新零工状态
     * 
     * @param workerIds 零工ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateWorkerProfileStatus(Long[] workerIds, String status);

    /**
     * 查询零工统计数据
     * 
     * @param userId 用户ID（可选）
     * @return 统计数据
     */
    public Map<String, Object> selectWorkerProfileStatistics(Long userId);

    /**
     * 查询零工详情（包含用户信息）
     * 
     * @param workerId 零工ID
     * @return 零工详情
     */
    public WorkerProfile selectWorkerProfileDetailByWorkerId(Long workerId);

    /**
     * 根据技能匹配零工信息
     * 
     * @param skills 技能列表
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileBySkills(List<String> skills, Integer limit);

    /**
     * 根据地理位置匹配零工信息
     * 
     * @param location 地理位置
     * @param radius 半径（公里）
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByLocation(String location, Double radius, Integer limit);

    /**
     * 根据薪资期望匹配零工信息
     * 
     * @param salaryMin 最低薪资
     * @param salaryMax 最高薪资
     * @param salaryType 薪资类型
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileBySalaryExpectation(Double salaryMin, Double salaryMax, String salaryType);

    /**
     * 查询相似的零工信息
     * 
     * @param workerProfile 零工信息
     * @param limit 限制数量
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectSimilarWorkerProfileList(WorkerProfile workerProfile, Integer limit);

    /**
     * 根据招聘信息匹配零工信息
     *
     * @param jobId 招聘信息ID
     * @param limit 限制数量
     * @return 匹配的零工信息集合
     */
    public List<WorkerProfile> matchWorkerProfileForJob(Long jobId, Integer limit);

    /**
     * 根据匹配参数查询零工信息
     *
     * @param matchParams 匹配参数
     * @return 匹配的零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByMatchParams(Map<String, Object> matchParams);

    /**
     * 智能推荐零工信息
     * 
     * @param employerId 雇主ID
     * @param limit 限制数量
     * @return 推荐的零工信息集合
     */
    public List<WorkerProfile> recommendWorkerProfileForEmployer(Long employerId, Integer limit);

    /**
     * 校验零工信息是否可以编辑
     * 
     * @param workerId 零工信息ID
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkWorkerProfileEditable(Long workerId, Long userId);

    /**
     * 校验零工信息是否可以删除
     * 
     * @param workerId 零工信息ID
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkWorkerProfileDeletable(Long workerId, Long userId);

    /**
     * 校验用户是否已有零工信息
     *
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkUserHasWorkerProfile(Long userId);

    // ==================== 核心匹配优化方法 ====================

    /**
     * 基于核心字段搜索零工信息
     *
     * @param jobTypesPreferred 偏好工作类型
     * @param workCategories 工作类别偏好
     * @param salaryTypePreference 薪资类型偏好
     * @param educationLevel 学历水平
     * @param keyword 关键词（姓名）
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByCoreFields(String jobTypesPreferred, String workCategories,
                                                              String salaryTypePreference, String educationLevel, String keyword);

    /**
     * 根据偏好工作类型查询零工信息
     *
     * @param jobType 工作类型
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByJobType(String jobType);

    /**
     * 根据工作类别偏好查询零工信息
     *
     * @param jobCategory 工作类别
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByJobCategory(String jobCategory);

    /**
     * 根据薪资类型偏好查询零工信息
     *
     * @param salaryType 薪资类型
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileBySalaryType(String salaryType);

    /**
     * 根据学历水平查询零工信息
     *
     * @param educationLevel 学历水平
     * @return 零工信息集合
     */
    public List<WorkerProfile> selectWorkerProfileByEducation(String educationLevel);

    /**
     * 获取所有偏好工作类型列表
     *
     * @return 工作类型集合
     */
    public List<String> selectAllJobTypesPreferred();

    /**
     * 获取所有工作类别偏好列表
     *
     * @return 工作类别集合
     */
    public List<String> selectAllWorkCategories();

    /**
     * 获取所有薪资类型偏好列表
     *
     * @return 薪资类型集合
     */
    public List<String> selectAllSalaryTypePreferences();

    /**
     * 获取所有学历水平列表
     *
     * @return 学历水平集合
     */
    public List<String> selectAllEducationLevels();
}
