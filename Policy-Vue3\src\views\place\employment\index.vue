<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用工标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入用工标题"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用工类型" prop="employmentType">
        <el-select v-model="queryParams.employmentType" placeholder="请选择用工类型" clearable>
          <el-option label="日结" value="日结" />
          <el-option label="周结" value="周结" />
          <el-option label="月结" value="月结" />
          <el-option label="计件" value="计件" />
        </el-select>
      </el-form-item>
      <el-form-item label="工作类别" prop="workCategory">
        <el-select v-model="queryParams.workCategory" placeholder="请选择工作类别" clearable>
          <el-option label="服务员" value="服务员" />
          <el-option label="保洁" value="保洁" />
          <el-option label="搬运工" value="搬运工" />
          <el-option label="销售" value="销售" />
          <el-option label="厨师助手" value="厨师助手" />
          <el-option label="快递员" value="快递员" />
          <el-option label="保安" value="保安" />
        </el-select>
      </el-form-item>
      <el-form-item label="薪资类型" prop="salaryType">
        <el-select v-model="queryParams.salaryType" placeholder="请选择薪资类型" clearable>
          <el-option label="小时薪" value="hourly" />
          <el-option label="日薪" value="daily" />
          <el-option label="月薪" value="monthly" />
          <el-option label="计件" value="piece" />
        </el-select>
      </el-form-item>
      <el-form-item label="区域" prop="regionCode">
        <el-select v-model="queryParams.regionCode" placeholder="请选择区域" clearable>
          <el-option
            v-for="region in regionOptions"
            :key="region.regionCode"
            :label="region.regionName"
            :value="region.regionCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度" prop="urgencyLevel">
        <el-select v-model="queryParams.urgencyLevel" placeholder="请选择紧急程度" clearable>
          <el-option label="紧急" value="urgent" />
          <el-option label="高" value="high" />
          <el-option label="普通" value="normal" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="草稿" value="draft" />
          <el-option label="已发布" value="published" />
          <el-option label="已暂停" value="paused" />
          <el-option label="已关闭" value="closed" />
          <el-option label="已完成" value="completed" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['place:employment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['place:employment:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['place:employment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['place:employment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="employmentInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用工ID" align="center" prop="employmentId" />
      <el-table-column label="用工标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="用工类型" align="center" prop="employmentType" />
      <el-table-column label="工作类别" align="center" prop="workCategory" />
      <el-table-column label="工作地点" align="center" prop="workLocation" :show-overflow-tooltip="true" />
      <el-table-column label="薪资类型" align="center" prop="salaryType" />
      <el-table-column label="薪资范围" align="center">
        <template #default="scope">
          <span v-if="scope.row.salaryMin && scope.row.salaryMax">
            {{ scope.row.salaryMin }}-{{ scope.row.salaryMax }}
          </span>
          <span v-else-if="scope.row.salaryMin">
            {{ scope.row.salaryMin }}起
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="需要人数" align="center" prop="positionsNeeded" />
      <el-table-column label="已招人数" align="center" prop="positionsFilled" />
      <el-table-column label="公司名称" align="center" prop="companyName" :show-overflow-tooltip="true" />
      <el-table-column label="联系人" align="center" prop="contactPerson" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="紧急程度" align="center" prop="urgencyLevel">
        <template #default="scope">
          <el-tag v-if="scope.row.urgencyLevel === 'urgent'" type="danger">紧急</el-tag>
          <el-tag v-else-if="scope.row.urgencyLevel === 'high'" type="warning">高</el-tag>
          <el-tag v-else-if="scope.row.urgencyLevel === 'normal'" type="info">普通</el-tag>
          <el-tag v-else-if="scope.row.urgencyLevel === 'low'" type="success">低</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 'draft'" type="info">草稿</el-tag>
          <el-tag v-else-if="scope.row.status === 'published'" type="success">已发布</el-tag>
          <el-tag v-else-if="scope.row.status === 'paused'" type="warning">已暂停</el-tag>
          <el-tag v-else-if="scope.row.status === 'closed'" type="danger">已关闭</el-tag>
          <el-tag v-else-if="scope.row.status === 'completed'" type="primary">已完成</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否推荐" align="center" prop="isFeatured">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isFeatured"/>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="viewCount" />
      <el-table-column label="申请次数" align="center" prop="applicationCount" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['place:employment:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['place:employment:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['place:employment:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用工信息对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form ref="employmentInfoRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="用工标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入用工标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="用工类型" prop="employmentType">
              <el-select v-model="form.employmentType" placeholder="请选择用工类型">
                <el-option label="日结" value="日结" />
                <el-option label="周结" value="周结" />
                <el-option label="月结" value="月结" />
                <el-option label="计件" value="计件" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工作类别" prop="workCategory">
              <el-select v-model="form.workCategory" placeholder="请选择工作类别">
                <el-option label="服务员" value="服务员" />
                <el-option label="保洁" value="保洁" />
                <el-option label="搬运工" value="搬运工" />
                <el-option label="销售" value="销售" />
                <el-option label="厨师助手" value="厨师助手" />
                <el-option label="快递员" value="快递员" />
                <el-option label="保安" value="保安" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="薪资类型" prop="salaryType">
              <el-select v-model="form.salaryType" placeholder="请选择薪资类型">
                <el-option label="小时薪" value="hourly" />
                <el-option label="日薪" value="daily" />
                <el-option label="月薪" value="monthly" />
                <el-option label="计件" value="piece" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工作地点" prop="workLocation">
              <el-input v-model="form.workLocation" placeholder="请输入工作地点" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="区域代码" prop="regionCode">
              <el-input v-model="form.regionCode" placeholder="请输入区域代码" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="区域名称" prop="regionName">
              <el-input v-model="form.regionName" placeholder="请输入区域名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="最低薪资" prop="salaryMin">
              <el-input-number v-model="form.salaryMin" :min="0" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最高薪资" prop="salaryMax">
              <el-input-number v-model="form.salaryMax" :min="0" :precision="2" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需要人数" prop="positionsNeeded">
              <el-input-number v-model="form.positionsNeeded" :min="1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="每日工时" prop="workHoursPerDay">
              <el-input-number v-model="form.workHoursPerDay" :min="1" :max="24" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每周工作天数" prop="workDaysPerWeek">
              <el-input-number v-model="form.workDaysPerWeek" :min="1" :max="7" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急程度" prop="urgencyLevel">
              <el-select v-model="form.urgencyLevel" placeholder="请选择紧急程度">
                <el-option label="紧急" value="urgent" />
                <el-option label="高" value="high" />
                <el-option label="普通" value="normal" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="选择开始日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="选择结束日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="学历要求" prop="educationRequired">
              <el-input v-model="form.educationRequired" placeholder="请输入学历要求" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经验要求" prop="experienceRequired">
              <el-input v-model="form.experienceRequired" placeholder="请输入经验要求" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别要求" prop="genderRequired">
              <el-select v-model="form.genderRequired" placeholder="请选择性别要求">
                <el-option label="不限" value="unlimited" />
                <el-option label="男" value="male" />
                <el-option label="女" value="female" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="最小年龄" prop="ageMin">
              <el-input-number v-model="form.ageMin" :min="16" :max="80" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大年龄" prop="ageMax">
              <el-input-number v-model="form.ageMax" :min="16" :max="80" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请截止时间" prop="applicationDeadline">
              <el-date-picker
                v-model="form.applicationDeadline"
                type="datetime"
                placeholder="选择申请截止时间"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入公司名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司地址" prop="companyAddress">
              <el-input v-model="form.companyAddress" placeholder="请输入公司地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工作描述" prop="workDescription">
          <el-input v-model="form.workDescription" type="textarea" placeholder="请输入工作描述" />
        </el-form-item>
        <el-form-item label="福利待遇" prop="welfareBenefits">
          <el-input v-model="form.welfareBenefits" type="textarea" placeholder="请输入福利待遇" />
        </el-form-item>
        <el-form-item label="公司描述" prop="companyDescription">
          <el-input v-model="form.companyDescription" type="textarea" placeholder="请输入公司描述" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="是否推荐" prop="isFeatured">
              <el-radio-group v-model="form.isFeatured">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否验证" prop="isVerified">
              <el-radio-group v-model="form.isVerified">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
                <el-option label="已暂停" value="paused" />
                <el-option label="已关闭" value="closed" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用工详情对话框 -->
    <el-dialog title="用工信息详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用工标题" :span="2">{{ detailForm.title }}</el-descriptions-item>
        <el-descriptions-item label="用工类型">{{ detailForm.employmentType }}</el-descriptions-item>
        <el-descriptions-item label="工作类别">{{ detailForm.workCategory }}</el-descriptions-item>
        <el-descriptions-item label="薪资类型">{{ detailForm.salaryType }}</el-descriptions-item>
        <el-descriptions-item label="薪资范围">{{ detailForm.salaryMin }}-{{ detailForm.salaryMax }}</el-descriptions-item>
        <el-descriptions-item label="工作地点" :span="2">{{ detailForm.workLocation }}</el-descriptions-item>
        <el-descriptions-item label="需要人数">{{ detailForm.positionsNeeded }}</el-descriptions-item>
        <el-descriptions-item label="已招人数">{{ detailForm.positionsFilled }}</el-descriptions-item>
        <el-descriptions-item label="每日工时">{{ detailForm.workHoursPerDay }}小时</el-descriptions-item>
        <el-descriptions-item label="每周工作天数">{{ detailForm.workDaysPerWeek }}天</el-descriptions-item>
        <el-descriptions-item label="开始日期">{{ detailForm.startDate }}</el-descriptions-item>
        <el-descriptions-item label="结束日期">{{ detailForm.endDate }}</el-descriptions-item>
        <el-descriptions-item label="学历要求">{{ detailForm.educationRequired }}</el-descriptions-item>
        <el-descriptions-item label="经验要求">{{ detailForm.experienceRequired }}</el-descriptions-item>
        <el-descriptions-item label="年龄要求">{{ detailForm.ageMin }}-{{ detailForm.ageMax }}岁</el-descriptions-item>
        <el-descriptions-item label="性别要求">{{ detailForm.genderRequired }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ detailForm.companyName }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailForm.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailForm.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="联系邮箱">{{ detailForm.contactEmail }}</el-descriptions-item>
        <el-descriptions-item label="紧急程度">{{ detailForm.urgencyLevel }}</el-descriptions-item>
        <el-descriptions-item label="申请截止时间">{{ detailForm.applicationDeadline }}</el-descriptions-item>
        <el-descriptions-item label="浏览次数">{{ detailForm.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="申请次数">{{ detailForm.applicationCount }}</el-descriptions-item>
        <el-descriptions-item label="工作描述" :span="2">{{ detailForm.workDescription }}</el-descriptions-item>
        <el-descriptions-item label="福利待遇" :span="2">{{ detailForm.welfareBenefits }}</el-descriptions-item>
        <el-descriptions-item label="公司描述" :span="2">{{ detailForm.companyDescription }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="EmploymentInfo">
import { listEmploymentInfo, getEmploymentInfo, delEmploymentInfo, addEmploymentInfo, updateEmploymentInfo } from "@/api/place/employment";

const { proxy } = getCurrentInstance();
const { sys_yes_no } = proxy.useDict('sys_yes_no');

const employmentInfoList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 选项数据
const regionOptions = ref([]);

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    employmentType: null,
    workCategory: null,
    salaryType: null,
    regionCode: null,
    urgencyLevel: null,
    status: null,
  },
  rules: {
    title: [
      { required: true, message: "用工标题不能为空", trigger: "blur" }
    ],
    employmentType: [
      { required: true, message: "用工类型不能为空", trigger: "change" }
    ],
    workCategory: [
      { required: true, message: "工作类别不能为空", trigger: "change" }
    ],
    salaryType: [
      { required: true, message: "薪资类型不能为空", trigger: "change" }
    ],
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询用工信息列表 */
function getList() {
  loading.value = true;
  listEmploymentInfo(queryParams.value).then(response => {
    employmentInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    employmentId: null,
    title: null,
    employmentType: null,
    workCategory: null,
    workLocation: null,
    regionCode: null,
    regionName: null,
    salaryType: null,
    salaryMin: null,
    salaryMax: null,
    workHoursPerDay: null,
    workDaysPerWeek: null,
    startDate: null,
    endDate: null,
    positionsNeeded: 1,
    positionsFilled: 0,
    educationRequired: null,
    experienceRequired: null,
    ageMin: null,
    ageMax: null,
    genderRequired: "unlimited",
    workDescription: null,
    welfareBenefits: null,
    contactPerson: null,
    contactPhone: null,
    contactEmail: null,
    companyName: null,
    companyAddress: null,
    companyDescription: null,
    urgencyLevel: "normal",
    applicationDeadline: null,
    status: "draft",
    isVerified: 0,
    isFeatured: 0,
    remark: null
  };
  proxy.resetForm("employmentInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.employmentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用工信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _employmentId = row.employmentId || ids.value
  getEmploymentInfo(_employmentId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改用工信息";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _employmentId = row.employmentId;
  getEmploymentInfo(_employmentId).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["employmentInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.employmentId != null) {
        updateEmploymentInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEmploymentInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _employmentIds = row.employmentId || ids.value;
  proxy.$modal.confirm('是否确认删除用工信息编号为"' + _employmentIds + '"的数据项？').then(function() {
    return delEmploymentInfo(_employmentIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('place/employment/export', {
    ...queryParams.value
  }, `employment_info_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  // 加载选项数据
  regionOptions.value = [
    { regionCode: '370202', regionName: '市南区' },
    { regionCode: '370203', regionName: '市北区' },
    { regionCode: '370212', regionName: '崂山区' }
  ];
});
</script>
