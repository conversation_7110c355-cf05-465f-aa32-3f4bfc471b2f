package com.sux.web.controller.training;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.TrainingInstitutionApplication;
import com.sux.system.service.ITrainingInstitutionApplicationService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 培训机构申请Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/training/institution/application")
public class TrainingInstitutionApplicationController extends BaseC<PERSON>roller
{
    @Autowired
    private ITrainingInstitutionApplicationService trainingInstitutionApplicationService;

    /**
     * 查询培训机构申请列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:list')")
    @GetMapping("/list")
    public TableDataInfo list(TrainingInstitutionApplication trainingInstitutionApplication)
    {
        startPage();
        List<TrainingInstitutionApplication> list = trainingInstitutionApplicationService.selectTrainingInstitutionApplicationList(trainingInstitutionApplication);
        return getDataTable(list);
    }

    /**
     * 导出培训机构申请列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:export')")
    @Log(title = "培训机构申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TrainingInstitutionApplication trainingInstitutionApplication)
    {
        List<TrainingInstitutionApplication> list = trainingInstitutionApplicationService.selectTrainingInstitutionApplicationList(trainingInstitutionApplication);
        ExcelUtil<TrainingInstitutionApplication> util = new ExcelUtil<TrainingInstitutionApplication>(TrainingInstitutionApplication.class);
        util.exportExcel(response, list, "培训机构申请数据");
    }

    /**
     * 获取培训机构申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(trainingInstitutionApplicationService.selectTrainingInstitutionApplicationByApplicationId(applicationId));
    }

    /**
     * 新增培训机构申请
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:add')")
    @Log(title = "培训机构申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody TrainingInstitutionApplication trainingInstitutionApplication)
    {
        if (!trainingInstitutionApplicationService.checkApplicationUnique(trainingInstitutionApplication)) {
            return error("新增培训机构申请失败，该机构或联系方式已申请此培训");
        }
        return toAjax(trainingInstitutionApplicationService.insertTrainingInstitutionApplication(trainingInstitutionApplication));
    }

    /**
     * 修改培训机构申请
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:edit')")
    @Log(title = "培训机构申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody TrainingInstitutionApplication trainingInstitutionApplication)
    {
        if (!trainingInstitutionApplicationService.checkApplicationUnique(trainingInstitutionApplication)) {
            return error("修改培训机构申请失败，该机构或联系方式已申请此培训");
        }
        return toAjax(trainingInstitutionApplicationService.updateTrainingInstitutionApplication(trainingInstitutionApplication));
    }

    /**
     * 删除培训机构申请
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:remove')")
    @Log(title = "培训机构申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(trainingInstitutionApplicationService.deleteTrainingInstitutionApplicationByApplicationIds(applicationIds));
    }

    /**
     * 审核培训机构申请
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:review')")
    @Log(title = "培训机构申请审核", businessType = BusinessType.UPDATE)
    @PutMapping("/review/{applicationId}")
    public AjaxResult review(@PathVariable Long applicationId, @RequestParam String status, @RequestParam(required = false) String reviewComment)
    {
        String reviewer = SecurityUtils.getUsername();
        return toAjax(trainingInstitutionApplicationService.reviewApplication(applicationId, status, reviewer, reviewComment));
    }

    /**
     * 批量审核培训机构申请
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:review')")
    @Log(title = "培训机构申请批量审核", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-review")
    public AjaxResult batchReview(@RequestParam Long[] applicationIds, @RequestParam String status, @RequestParam(required = false) String reviewComment)
    {
        String reviewer = SecurityUtils.getUsername();
        return toAjax(trainingInstitutionApplicationService.batchReviewApplications(applicationIds, status, reviewer, reviewComment));
    }

    /**
     * 取消培训机构申请
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:cancel')")
    @Log(title = "培训机构申请", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{applicationId}")
    public AjaxResult cancel(@PathVariable Long applicationId)
    {
        return toAjax(trainingInstitutionApplicationService.cancelApplication(applicationId));
    }

    /**
     * 获取某个培训订单的机构申请列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:list')")
    @GetMapping("/order/{orderId}")
    public AjaxResult getApplicationsByOrderId(@PathVariable Long orderId)
    {
        List<TrainingInstitutionApplication> list = trainingInstitutionApplicationService.getApplicationsByOrderId(orderId);
        return success(list);
    }

    /**
     * 统计某个培训订单的机构申请数量
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:list')")
    @GetMapping("/count/{orderId}")
    public AjaxResult countApplicationsByOrderId(@PathVariable Long orderId)
    {
        int count = trainingInstitutionApplicationService.countApplicationsByOrderId(orderId);
        return success(count);
    }

    /**
     * 统计某个培训订单的已通过机构申请数量
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:list')")
    @GetMapping("/approved-count/{orderId}")
    public AjaxResult countApprovedApplicationsByOrderId(@PathVariable Long orderId)
    {
        int count = trainingInstitutionApplicationService.countApprovedApplicationsByOrderId(orderId);
        return success(count);
    }

    /**
     * 根据申请状态统计数量
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:list')")
    @GetMapping("/count-by-status/{status}")
    public AjaxResult countByStatus(@PathVariable String status)
    {
        int count = trainingInstitutionApplicationService.countByStatus(status);
        return success(count);
    }

    /**
     * 获取待审核的申请列表
     */
    @PreAuthorize("@ss.hasPermi('training:institution:application:list')")
    @GetMapping("/pending")
    public AjaxResult getPendingApplications()
    {
        List<TrainingInstitutionApplication> list = trainingInstitutionApplicationService.selectPendingApplications();
        return success(list);
    }
}
