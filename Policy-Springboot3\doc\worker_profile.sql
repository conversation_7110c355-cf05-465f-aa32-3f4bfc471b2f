/*
 Navicat Premium Data Transfer

 Source Server         : 8.0.40_3306
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : bussine-web-policy

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 23/07/2025 22:04:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for worker_profile
-- ----------------------------
DROP TABLE IF EXISTS `worker_profile`;
CREATE TABLE `worker_profile`  (
  `worker_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '真实姓名',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '性别（male/female）',
  `age` int(0) NULL DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `current_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前所在地（保留但不作为主要匹配条件）',
  `education_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学历水平（不限/初中/高中/中专/大专/本科/硕士/博士）',
  `work_experience_years` int(0) NULL DEFAULT NULL COMMENT '工作经验年数',
  `work_categories` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作类别偏好（JSON数组：服务员/保洁/搬运工/销售/厨师助手/快递员/保安等）',
  `job_types_preferred` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '偏好工作类型（JSON数组：全职/兼职/临时工/小时工）',
  `skills` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '技能列表（JSON格式）',
  `salary_expectation_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最低薪资',
  `salary_expectation_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '期望最高薪资',
  `salary_type_preference` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '薪资类型偏好（hourly/daily/monthly/piece）',
  `availability_start_date` date NULL DEFAULT NULL COMMENT '可开始工作日期',
  `work_days_per_week` int(0) NULL DEFAULT NULL COMMENT '每周可工作天数',
  `work_hours_per_day` int(0) NULL DEFAULT NULL COMMENT '每日可工作小时数',
  `profile_photo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像照片URL',
  `self_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自我介绍',
  `rating_average` decimal(3, 2) NULL COMMENT '平均评分',
  `rating_count` int(0) NULL DEFAULT 0 COMMENT '评分次数',
  `completed_jobs` int(0) NULL DEFAULT 0 COMMENT '完成工作数量',
  `success_rate` decimal(5, 2) NULL COMMENT '成功率',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'active' COMMENT '状态（active/inactive/suspended/banned）',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否已实名验证（0否 1是）',
  `last_active_time` datetime(0) NULL DEFAULT NULL COMMENT '最后活跃时间',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`worker_id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id`) USING BTREE,
  INDEX `idx_education_level`(`education_level`) USING BTREE,
  INDEX `idx_salary_type`(`salary_type_preference`) USING BTREE,
  INDEX `idx_salary_expectation`(`salary_expectation_min`, `salary_expectation_max`) USING BTREE,
  INDEX `idx_work_experience`(`work_experience_years`) USING BTREE,
  INDEX `idx_match_core`(`education_level`, `salary_type_preference`, `status`) USING BTREE,
  INDEX `idx_match_salary`(`salary_type_preference`, `salary_expectation_min`, `salary_expectation_max`) USING BTREE,
  INDEX `idx_real_name`(`real_name`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_rating`(`rating_average`) USING BTREE,
  INDEX `idx_verified`(`is_verified`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '零工信息表（核心匹配优化版）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_profile
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
