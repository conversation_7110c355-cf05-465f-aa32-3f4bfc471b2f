// 用工信息表格配置
export function createEmploymentTableOption(proxy) {
  return {
    // 表格列配置
    columns: [
      {
        prop: 'employmentId',
        label: '用工ID',
        width: 80,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: false
      },
      {
        prop: 'title',
        label: '用工标题',
        minWidth: 200,
        align: 'left',
        showOverflowTooltip: true,
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入用工标题'
      },
      {
        prop: 'employmentType',
        label: '用工类型',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        required: true,
        formType: 'select',
        placeholder: '请选择用工类型',
        options: [
          { label: '日结', value: '日结' },
          { label: '周结', value: '周结' },
          { label: '月结', value: '月结' },
          { label: '计件', value: '计件' }
        ]
      },
      {
        prop: 'workCategory',
        label: '工作类别',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        required: true,
        formType: 'select',
        placeholder: '请选择工作类别',
        options: [
          { label: '服务员', value: '服务员' },
          { label: '保洁', value: '保洁' },
          { label: '搬运工', value: '搬运工' },
          { label: '销售', value: '销售' },
          { label: '厨师助手', value: '厨师助手' },
          { label: '快递员', value: '快递员' },
          { label: '保安', value: '保安' }
        ]
      },
      {
        prop: 'workLocation',
        label: '工作地点',
        minWidth: 150,
        align: 'left',
        showOverflowTooltip: true,
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入工作地点'
      },
      {
        prop: 'salaryType',
        label: '薪资类型',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: true,
        showInForm: true,
        required: true,
        formType: 'select',
        placeholder: '请选择薪资类型',
        options: [
          { label: '小时薪', value: 'hourly' },
          { label: '日薪', value: 'daily' },
          { label: '月薪', value: 'monthly' },
          { label: '计件', value: 'piece' }
        ]
      },
      {
        prop: 'salaryRange',
        label: '薪资范围',
        width: 120,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: false,
        slotName: 'salaryRange'
      },
      {
        prop: 'salaryMin',
        label: '最低薪资',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入最低薪资'
      },
      {
        prop: 'salaryMax',
        label: '最高薪资',
        width: 100,
        align: 'center',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入最高薪资'
      },
      {
        prop: 'positionsNeeded',
        label: '需要人数',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'number',
        placeholder: '请输入需要人数'
      },
      {
        prop: 'positionsFilled',
        label: '已招人数',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        formType: 'number',
        placeholder: '请输入已招人数'
      },
      {
        prop: 'companyName',
        label: '公司名称',
        minWidth: 150,
        align: 'left',
        showOverflowTooltip: true,
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入公司名称'
      },
      {
        prop: 'contactPerson',
        label: '联系人',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入联系人'
      },
      {
        prop: 'contactPhone',
        label: '联系电话',
        width: 120,
        align: 'center',
        showInTable: true,
        showInSearch: false,
        showInForm: true,
        required: true,
        formType: 'input',
        placeholder: '请输入联系电话'
      },
      {
        prop: 'urgencyLevel',
        label: '紧急程度',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        formType: 'select',
        placeholder: '请选择紧急程度',
        slotName: 'urgencyLevel',
        options: [
          { label: '紧急', value: 'urgent' },
          { label: '高', value: 'high' },
          { label: '普通', value: 'normal' },
          { label: '低', value: 'low' }
        ]
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: true,
        formType: 'select',
        placeholder: '请选择状态',
        slotName: 'status',
        options: [
          { label: '草稿', value: 'draft' },
          { label: '已发布', value: 'published' },
          { label: '已暂停', value: 'paused' },
          { label: '已关闭', value: 'closed' },
          { label: '已完成', value: 'completed' }
        ]
      },
      {
        prop: 'workDescription',
        label: '工作描述',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'textarea',
        placeholder: '请输入工作描述'
      },
      {
        prop: 'welfareBenefits',
        label: '福利待遇',
        showInTable: false,
        showInSearch: false,
        showInForm: true,
        formType: 'textarea',
        placeholder: '请输入福利待遇'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 160,
        align: 'center',
        showInTable: true,
        showInSearch: true,
        showInForm: false,
        formType: 'daterange',
        placeholder: '请选择创建时间'
      }
    ],
    
    // 表单配置
    formOptions: {
      labelWidth: '120px',
      size: 'default',
      rules: {
        title: [
          { required: true, message: '用工标题不能为空', trigger: 'blur' }
        ],
        employmentType: [
          { required: true, message: '用工类型不能为空', trigger: 'change' }
        ],
        workCategory: [
          { required: true, message: '工作类别不能为空', trigger: 'change' }
        ],
        workLocation: [
          { required: true, message: '工作地点不能为空', trigger: 'blur' }
        ],
        salaryType: [
          { required: true, message: '薪资类型不能为空', trigger: 'change' }
        ],
        positionsNeeded: [
          { required: true, message: '需要人数不能为空', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '公司名称不能为空', trigger: 'blur' }
        ],
        contactPerson: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' }
        ]
      }
    }
  }
}
