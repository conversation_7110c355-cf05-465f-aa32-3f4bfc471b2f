/**
 * 招聘信息匹配页面JavaScript（核心匹配优化版）
 * 专注于展示基于工作类型、工作类别、薪资类型、学历的精准匹配结果
 */

// 公用模块html
headerBar()
footerBar()

// 全局变量
var currentJobId = getUrlParam('jobId') || getUrlParam('id') || '1';
var currentJobInfo = null;
var matchResults = [];
var filteredResults = [];

// 获取URL参数
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

// 页面初始化
$(document).ready(function() {
    console.log('招聘匹配页面初始化，招聘ID:', currentJobId);
    loadJobDetail();
    loadMatchResults();
});

// 原生Ajax请求函数
function jobMatchAjaxRequest(url, params, callback) {
    var baseUrl = 'http://localhost:80/sux-admin/';

    // 构建查询参数
    var queryString = '';
    if (params && typeof params === 'object') {
        var paramArray = [];
        for (var key in params) {
            if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined) {
                paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
            }
        }
        queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
    }

    var xhr = new XMLHttpRequest();
    xhr.open('GET', baseUrl + url + queryString, true);
    xhr.timeout = 30000;
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '解析响应数据失败',
                            data: null
                        });
                    }
                }
            } else {
                console.error('请求失败:', xhr.status, xhr.statusText);
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                        data: null
                    });
                }
            }
        }
    };

    xhr.ontimeout = function() {
        console.error('请求超时');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '请求超时',
                data: null
            });
        }
    };

    xhr.onerror = function() {
        console.error('请求发生错误');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '网络错误',
                data: null
            });
        }
    };

    xhr.send();
}

// 加载招聘信息详情
function loadJobDetail() {
    console.log('加载招聘详情，ID:', currentJobId);

    jobMatchAjaxRequest('public/job/postings/' + currentJobId, {}, function(data) {
        if (data.code == 0 && data.data) {
            currentJobInfo = data.data;
            renderJobDetail(data.data);
        } else {
            console.error('获取招聘信息失败：', data.msg);
            // 加载模拟数据
            loadMockJobDetail();
        }
    });
}

// 渲染招聘信息详情
function renderJobDetail(job) {
    document.getElementById('jobDetailTitle').textContent = job.jobTitle || '未知职位';
    
    // 格式化薪资显示
    var salaryText = '面议';
    if (job.salaryMin && job.salaryMax) {
        salaryText = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + (job.salaryType || '月');
    } else if (job.salaryMin) {
        salaryText = '￥' + job.salaryMin + '+/' + (job.salaryType || '月');
    }
    document.getElementById('jobDetailSalary').textContent = salaryText;
    
    document.getElementById('jobDetailType').textContent = job.jobType || '--';
    document.getElementById('jobDetailLocation').textContent = job.workLocation || '--';
    document.getElementById('jobDetailCategory').textContent = job.jobCategory || '--';
    
    // 格式化发布时间
    if (job.createTime) {
        var createDate = new Date(job.createTime);
        document.getElementById('jobDetailPublishTime').textContent = createDate.toLocaleDateString();
    } else {
        document.getElementById('jobDetailPublishTime').textContent = '--';
    }
}

// 加载匹配结果
function loadMatchResults() {
    console.log('加载匹配结果，招聘ID:', currentJobId);

    var loadingIndicator = document.getElementById('loadingIndicator');
    var resultsList = document.getElementById('matchResultsList');

    loadingIndicator.style.display = 'block';
    resultsList.innerHTML = '';

    // 使用优化的简化匹配API
    jobMatchAjaxRequest('public/job/simple/postings/' + currentJobId + '/match-workers', {limit: 20}, function(data) {
        loadingIndicator.style.display = 'none';

        if (data.code == 0 && data.data) {
            matchResults = data.data;
            filteredResults = [...matchResults];
            renderMatchResults();
            updateMatchCount();
        } else {
            console.error('获取匹配结果失败：', data.msg);
            // 如果简化API失败，尝试原始API
            jobMatchAjaxRequest('public/job/postings/' + currentJobId + '/match-workers', {limit: 20}, function(fallbackData) {
                if (fallbackData.code == 0 && fallbackData.data) {
                    matchResults = fallbackData.data;
                    filteredResults = [...matchResults];
                    renderMatchResults();
                    updateMatchCount();
                } else {
                    // 加载模拟匹配数据
                    loadMockMatchResults();
                }
            });
        }
    });
}

// 渲染匹配结果
function renderMatchResults() {
    var resultsList = document.getElementById('matchResultsList');
    
    if (filteredResults.length === 0) {
        resultsList.innerHTML = '<div class="no-results"><p>暂无匹配的零工</p></div>';
        return;
    }
    
    var html = '';
    filteredResults.forEach(function(item) {
        var worker = item.worker;
        var similarity = item.similarityPercentage || 0;
        
        html += `
            <div class="matchResultItem">
                <div class="workerAvatar">
                    <img src="${worker.profilePhoto || '../public/images/default-avatar.png'}" alt="头像">
                </div>
                <div class="workerMainInfo">
                    <h3 class="workerName">${worker.realName || worker.nickname || '未知'}</h3>
                    <div class="workerTags">
                        ${generateWorkerTags(worker)}
                    </div>
                    <div class="workerStats">
                        <div class="workerStat">
                            <span class="statIcon">📍</span>
                            <span>${worker.currentLocation || '位置未知'}</span>
                        </div>
                        <div class="workerStat">
                            <span class="statIcon">💼</span>
                            <span>${worker.workExperienceYears || 0}年经验</span>
                        </div>
                        <div class="workerStat">
                            <span class="statIcon">⭐</span>
                            <span>${worker.ratingAverage || 0}分</span>
                        </div>
                        <div class="workerStat">
                            <span class="statIcon">✅</span>
                            <span>${worker.completedJobs || 0}个完成</span>
                        </div>
                    </div>
                </div>
                <div class="matchScoreSection">
                    <div class="similarityCircle" style="--percentage: ${similarity}%">
                        <div class="similarityInner">
                            <span class="similarityText">${similarity}%</span>
                        </div>
                    </div>
                    <p class="matchLabel">匹配度</p>
                </div>
                <div class="workerActions">
                    <button class="actionBtn btn-contact" onclick="contactWorker(${worker.workerId})">联系</button>
                    <button class="actionBtn btn-view" onclick="viewWorkerDetail(${worker.workerId})">查看详情</button>
                </div>
            </div>
        `;
    });
    
    resultsList.innerHTML = html;
}

// 生成零工标签
function generateWorkerTags(worker) {
    var tags = [];
    
    if (worker.workCategories) {
        var categories = worker.workCategories.split(',');
        categories.slice(0, 3).forEach(function(cat) {
            tags.push(`<span class="workerTag">${cat.trim()}</span>`);
        });
    }
    
    if (worker.isVerified == 1) {
        tags.push('<span class="workerTag" style="background: #e8f5e8; color: #2e7d32;">已认证</span>');
    }
    
    if (worker.healthCertificate) {
        tags.push('<span class="workerTag" style="background: #fff3e0; color: #f57c00;">健康证</span>');
    }
    
    return tags.join('');
}

// 更新匹配数量显示
function updateMatchCount() {
    var countElement = document.getElementById('matchCount');
    countElement.textContent = `找到 ${filteredResults.length} 个匹配的零工`;
}

// 排序匹配结果
function sortMatches() {
    var sortBy = document.getElementById('sortBy').value;
    
    filteredResults.sort(function(a, b) {
        switch(sortBy) {
            case 'similarity':
                return (b.similarityPercentage || 0) - (a.similarityPercentage || 0);
            case 'rating':
                return (b.worker.ratingAverage || 0) - (a.worker.ratingAverage || 0);
            case 'experience':
                return (b.worker.workExperienceYears || 0) - (a.worker.workExperienceYears || 0);
            case 'completedJobs':
                return (b.worker.completedJobs || 0) - (a.worker.completedJobs || 0);
            default:
                return 0;
        }
    });
    
    renderMatchResults();
}

// 筛选匹配结果
function filterMatches() {
    var minSimilarity = parseInt(document.getElementById('minSimilarity').value) || 0;
    
    filteredResults = matchResults.filter(function(item) {
        return (item.similarityPercentage || 0) >= minSimilarity;
    });
    
    renderMatchResults();
    updateMatchCount();
}

// 刷新匹配结果
function refreshMatches() {
    loadMatchResults();
}

// 联系零工
function contactWorker(workerId) {
    // 获取零工详情
    jobMatchAjaxRequest('public/job/workers/' + workerId, {}, function(data) {
        if (data.code == 0 && data.data) {
            showContactModal(data.data);
        } else {
            console.error('获取零工信息失败');
            // 显示默认联系信息
            showContactModal({
                realName: '零工用户',
                phone: '请联系平台客服',
                email: '暂无邮箱',
                wechat: '暂无微信',
                currentLocation: '未知'
            });
        }
    });
}

// 显示联系模态框
function showContactModal(worker) {
    var modal = document.getElementById('contactModal');
    var contactInfo = document.getElementById('contactInfo');
    
    var infoHtml = `
        <h4>${worker.realName || worker.nickname || '未知'}</h4>
        <p><strong>手机：</strong>${worker.phone || '未提供'}</p>
        <p><strong>邮箱：</strong>${worker.email || '未提供'}</p>
        <p><strong>微信：</strong>${worker.wechat || '未提供'}</p>
        <p><strong>所在地：</strong>${worker.currentLocation || '未知'}</p>
    `;
    
    contactInfo.innerHTML = infoHtml;
    modal.style.display = 'flex';
}

// 关闭联系模态框
function closeContactModal() {
    document.getElementById('contactModal').style.display = 'none';
    document.getElementById('contactMessage').value = '';
}

// 发送消息
function sendMessage() {
    var message = document.getElementById('contactMessage').value.trim();
    if (!message) {
        showToast('请输入消息内容', 'warning');
        return;
    }
    
    // 这里应该调用发送消息的API
    showToast('消息发送成功！', 'success');
    closeContactModal();
}

// 查看零工详情
function viewWorkerDetail(workerId) {
    jobMatchAjaxRequest('public/job/workers/' + workerId, {}, function(data) {
        if (data.code == 0 && data.data) {
            showWorkerDetailModal(data.data);
        } else {
            console.error('获取零工详情失败');
            // 显示默认零工信息
            showWorkerDetailModal({
                realName: '零工用户',
                nickname: '零工' + workerId,
                workCategories: '暂无分类',
                currentLocation: '未知',
                age: '--',
                gender: 'unknown',
                educationLevel: '未知',
                workExperienceYears: 0,
                completedJobs: 0,
                successRate: 0,
                ratingAverage: 0,
                ratingCount: 0,
                selfIntroduction: '暂无介绍'
            });
        }
    });
}

// 显示零工详情模态框
function showWorkerDetailModal(worker) {
    var modal = document.getElementById('workerDetailModal');
    var content = document.getElementById('workerDetailContent');
    
    var detailHtml = generateWorkerDetailHtml(worker);
    content.innerHTML = detailHtml;
    modal.style.display = 'flex';
}

// 生成零工详情HTML
function generateWorkerDetailHtml(worker) {
    return `
        <div class="worker-detail-content">
            <div class="worker-header">
                <img src="${worker.profilePhoto || '../public/images/default-avatar.png'}" alt="头像" class="worker-detail-avatar">
                <div class="worker-basic-info">
                    <h3>${worker.realName || worker.nickname || '未知'}</h3>
                    <p class="worker-title">${worker.workCategories || '暂无分类'}</p>
                    <div class="worker-rating">
                        <span class="rating-stars">${generateStars(worker.ratingAverage || 0)}</span>
                        <span class="rating-text">${worker.ratingAverage || 0}分 (${worker.ratingCount || 0}评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="worker-sections">
                <div class="worker-section">
                    <h4>基本信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">性别：</span>
                            <span class="value">${worker.gender === 'male' ? '男' : worker.gender === 'female' ? '女' : '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">年龄：</span>
                            <span class="value">${worker.age || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">学历：</span>
                            <span class="value">${worker.educationLevel || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">工作经验：</span>
                            <span class="value">${worker.workExperienceYears || 0}年</span>
                        </div>
                    </div>
                </div>
                
                <div class="worker-section">
                    <h4>工作信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">完成工作：</span>
                            <span class="value">${worker.completedJobs || 0}个</span>
                        </div>
                        <div class="info-item">
                            <span class="label">成功率：</span>
                            <span class="value">${worker.successRate || 0}%</span>
                        </div>
                        <div class="info-item">
                            <span class="label">期望薪资：</span>
                            <span class="value">${formatSalaryExpectation(worker)}</span>
                        </div>
                    </div>
                </div>
                
                ${worker.selfIntroduction ? `
                <div class="worker-section">
                    <h4>自我介绍</h4>
                    <p class="intro-text">${worker.selfIntroduction}</p>
                </div>
                ` : ''}
            </div>
        </div>
    `;
}

// 生成星级评分
function generateStars(rating) {
    var stars = '';
    var fullStars = Math.floor(rating);
    var hasHalfStar = rating % 1 >= 0.5;
    
    for (var i = 0; i < fullStars; i++) {
        stars += '⭐';
    }
    if (hasHalfStar) {
        stars += '⭐';
    }
    
    return stars;
}

// 格式化薪资期望
function formatSalaryExpectation(worker) {
    if (worker.salaryExpectationMin && worker.salaryExpectationMax) {
        return `￥${worker.salaryExpectationMin}-${worker.salaryExpectationMax}/${worker.salaryTypePreference || '月'}`;
    } else if (worker.salaryExpectationMin) {
        return `￥${worker.salaryExpectationMin}+/${worker.salaryTypePreference || '月'}`;
    }
    return '面议';
}

// 关闭零工详情模态框
function closeWorkerDetailModal() {
    document.getElementById('workerDetailModal').style.display = 'none';
}

// 加载模拟招聘信息详情
function loadMockJobDetail() {
    console.log('加载模拟招聘信息详情');
    var mockJob = {
        jobId: jobId,
        jobTitle: 'Java高级开发工程师',
        jobDescription: '负责公司核心业务系统的开发和维护，要求熟练掌握Spring Boot、MyBatis等技术栈',
        jobType: '全职',
        jobCategory: 'IT技术',
        workLocation: '青岛市市南区',
        salaryMin: 8000,
        salaryMax: 15000,
        salaryType: '月',
        createTime: new Date().toISOString(),
        skillsRequired: 'Java,Spring Boot,MySQL,Redis',
        workExperienceRequired: '3-5年',
        educationRequired: '本科',
        companyName: '青岛科技有限公司',
        contactPerson: '张经理',
        contactPhone: '13800138001'
    };

    currentJobData = mockJob;
    renderJobDetail(mockJob);
}

// 加载模拟匹配结果
function loadMockMatchResults() {
    console.log('加载模拟匹配结果');
    var mockResults = [
        {
            worker: {
                workerId: 1,
                realName: '张三',
                nickname: '张工程师',
                profilePhoto: '',
                workCategories: 'IT技术,软件开发',
                currentLocation: '青岛市市南区',
                workExperienceYears: 5,
                ratingAverage: 4.8,
                completedJobs: 25,
                successRate: 96,
                isVerified: 1,
                healthCertificate: true,
                skills: 'Java,Spring Boot,MySQL,Redis,Vue.js',
                age: 28,
                gender: 'male',
                educationLevel: '本科',
                selfIntroduction: '5年Java开发经验，熟练掌握Spring全家桶，有大型项目开发经验。'
            },
            similarity: 0.92,
            similarityPercentage: 92,
            matchScore: 88
        },
        {
            worker: {
                workerId: 2,
                realName: '李四',
                nickname: '李开发',
                profilePhoto: '',
                workCategories: 'IT技术,前端开发',
                currentLocation: '青岛市崂山区',
                workExperienceYears: 3,
                ratingAverage: 4.5,
                completedJobs: 18,
                successRate: 94,
                isVerified: 1,
                healthCertificate: false,
                skills: 'JavaScript,Vue.js,React,Node.js',
                age: 26,
                gender: 'male',
                educationLevel: '大专',
                selfIntroduction: '3年前端开发经验，熟练使用各种前端框架。'
            },
            similarity: 0.75,
            similarityPercentage: 75,
            matchScore: 78
        },
        {
            worker: {
                workerId: 3,
                realName: '王五',
                nickname: '王全栈',
                profilePhoto: '',
                workCategories: 'IT技术,全栈开发',
                currentLocation: '青岛市市北区',
                workExperienceYears: 4,
                ratingAverage: 4.6,
                completedJobs: 22,
                successRate: 95,
                isVerified: 1,
                healthCertificate: true,
                skills: 'Java,Python,JavaScript,MySQL,MongoDB',
                age: 30,
                gender: 'male',
                educationLevel: '本科',
                selfIntroduction: '4年全栈开发经验，前后端都能胜任。'
            },
            similarity: 0.85,
            similarityPercentage: 85,
            matchScore: 82
        }
    ];

    matchResults = mockResults;
    filteredResults = [...matchResults];
    renderMatchResults();
    updateMatchCount();
    console.log('模拟匹配结果已加载，共', mockResults.length, '条数据');
}
