package com.sux.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sux.system.domain.JobPosting;

import java.util.List;
import java.util.Map;

/**
 * 招聘信息Service接口（核心匹配优化版）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IJobPostingService extends IService<JobPosting>
{
    /**
     * 查询招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingList(JobPosting jobPosting);

    /**
     * 查询招聘信息
     * 
     * @param jobId 招聘信息主键
     * @return 招聘信息
     */
    public JobPosting selectJobPostingByJobId(Long jobId);

    /**
     * 新增招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    public int insertJobPosting(JobPosting jobPosting);

    /**
     * 修改招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    public int updateJobPosting(JobPosting jobPosting);

    /**
     * 批量删除招聘信息
     * 
     * @param jobIds 需要删除的招聘信息主键集合
     * @return 结果
     */
    public int deleteJobPostingByJobIds(Long[] jobIds);

    /**
     * 删除招聘信息信息
     * 
     * @param jobId 招聘信息主键
     * @return 结果
     */
    public int deleteJobPostingByJobId(Long jobId);

    /**
     * 查询我发布的招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @param publisherUserId 发布者用户ID
     * @return 招聘信息集合
     */
    public List<JobPosting> selectMyJobPostingList(JobPosting jobPosting, Long publisherUserId);

    /**
     * 查询已发布的招聘信息列表（公开接口）
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息集合
     */
    public List<JobPosting> selectPublishedJobPostingList(JobPosting jobPosting);

    /**
     * 发布招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int publishJobPosting(Long jobId);

    /**
     * 暂停招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int pauseJobPosting(Long jobId);

    /**
     * 关闭招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int closeJobPosting(Long jobId);

    /**
     * 完成招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int completeJobPosting(Long jobId);

    /**
     * 增加招聘信息浏览次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int increaseViewCount(Long jobId);

    /**
     * 增加招聘信息申请次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int increaseApplicationCount(Long jobId);

    /**
     * 更新招聘信息已招聘人数
     * 
     * @param jobId 招聘信息ID
     * @param positionsFilled 已招聘人数
     * @return 结果
     */
    public int updatePositionsFilled(Long jobId, Integer positionsFilled);

    /**
     * 查询热门招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectHotJobPostingList(Integer limit);

    /**
     * 查询推荐招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectFeaturedJobPostingList(Integer limit);

    /**
     * 查询紧急招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectUrgentJobPostingList(Integer limit);

    /**
     * 根据工作类别统计招聘信息数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectJobPostingCountByCategory();

    /**
     * 根据工作地点统计招聘信息数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectJobPostingCountByLocation();

    /**
     * 根据薪资范围统计招聘信息数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectJobPostingCountBySalaryRange();

    /**
     * 查询即将截止的招聘信息
     * 
     * @param days 天数
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingExpiringSoon(Integer days);

    /**
     * 查询招聘信息详情（包含发布者信息）
     * 
     * @param jobId 招聘信息ID
     * @return 招聘信息详情
     */
    public JobPosting selectJobPostingDetailByJobId(Long jobId);

    /**
     * 根据关键词搜索招聘信息
     * 
     * @param keyword 关键词
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByKeyword(String keyword);

    /**
     * 查询相似的招聘信息
     * 
     * @param jobPosting 招聘信息
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectSimilarJobPostingList(JobPosting jobPosting, Integer limit);

    /**
     * 批量更新招聘信息状态
     * 
     * @param jobIds 招聘信息ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateJobPostingStatus(Long[] jobIds, String status);

    /**
     * 查询招聘信息统计数据
     * 
     * @param publisherUserId 发布者用户ID（可选）
     * @return 统计数据
     */
    public Map<String, Object> selectJobPostingStatistics(Long publisherUserId);

    /**
     * 根据零工信息匹配招聘信息
     * 
     * @param workerId 零工ID
     * @param limit 限制数量
     * @return 匹配的招聘信息集合
     */
    public List<JobPosting> matchJobPostingForWorker(Long workerId, Integer limit);

    /**
     * 智能推荐招聘信息
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐的招聘信息集合
     */
    public List<JobPosting> recommendJobPostingForUser(Long userId, Integer limit);

    /**
     * 校验招聘信息标题是否唯一
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    public boolean checkJobTitleUnique(JobPosting jobPosting);

    /**
     * 校验招聘信息是否可以编辑
     * 
     * @param jobId 招聘信息ID
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkJobPostingEditable(Long jobId, Long userId);

    /**
     * 校验招聘信息是否可以删除
     *
     * @param jobId 招聘信息ID
     * @param userId 用户ID
     * @return 结果
     */
    public boolean checkJobPostingDeletable(Long jobId, Long userId);

    /**
     * 根据招聘信息匹配零工（带相似度评分）
     *
     * @param jobId 招聘信息ID
     * @param limit 限制数量
     * @return 匹配结果列表（包含零工信息和相似度评分）
     */
    public List<Map<String, Object>> matchWorkersWithSimilarity(Long jobId, Integer limit);

    /**
     * 计算招聘信息与零工的相似度
     *
     * @param jobId 招聘信息ID
     * @param workerId 零工ID
     * @return 相似度评分（0.0-1.0）
     */
    public Double calculateJobWorkerSimilarity(Long jobId, Long workerId);

    // ==================== 核心匹配优化方法 ====================

    /**
     * 基于核心字段搜索招聘信息
     *
     * @param jobType 工作类型
     * @param jobCategory 工作类别
     * @param salaryType 薪资类型
     * @param educationRequired 学历要求
     * @param keyword 关键词（职位名称）
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByCoreFields(String jobType, String jobCategory,
                                                        String salaryType, String educationRequired, String keyword);

    /**
     * 根据工作类型查询招聘信息
     *
     * @param jobType 工作类型
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByJobType(String jobType);

    /**
     * 根据工作类别查询招聘信息
     *
     * @param jobCategory 工作类别
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByJobCategory(String jobCategory);

    /**
     * 根据薪资类型查询招聘信息
     *
     * @param salaryType 薪资类型
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingBySalaryType(String salaryType);

    /**
     * 根据学历要求查询招聘信息
     *
     * @param educationRequired 学历要求
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByEducation(String educationRequired);

    /**
     * 获取所有工作类型列表
     *
     * @return 工作类型集合
     */
    public List<String> selectAllJobTypes();

    /**
     * 获取所有工作类别列表
     *
     * @return 工作类别集合
     */
    public List<String> selectAllJobCategories();

    /**
     * 获取所有薪资类型列表
     *
     * @return 薪资类型集合
     */
    public List<String> selectAllSalaryTypes();

    /**
     * 获取所有学历要求列表
     *
     * @return 学历要求集合
     */
    public List<String> selectAllEducationRequirements();
}
