package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.TrainingInstitutionApplication;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 培训机构申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface TrainingInstitutionApplicationMapper extends BaseMapper<TrainingInstitutionApplication>
{
    /**
     * 查询培训机构申请
     * 
     * @param applicationId 培训机构申请主键
     * @return 培训机构申请
     */
    public TrainingInstitutionApplication selectTrainingInstitutionApplicationByApplicationId(Long applicationId);

    /**
     * 查询培训机构申请列表
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 培训机构申请集合
     */
    public List<TrainingInstitutionApplication> selectTrainingInstitutionApplicationList(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 新增培训机构申请
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 结果
     */
    public int insertTrainingInstitutionApplication(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 修改培训机构申请
     * 
     * @param trainingInstitutionApplication 培训机构申请
     * @return 结果
     */
    public int updateTrainingInstitutionApplication(TrainingInstitutionApplication trainingInstitutionApplication);

    /**
     * 删除培训机构申请
     * 
     * @param applicationId 培训机构申请主键
     * @return 结果
     */
    public int deleteTrainingInstitutionApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除培训机构申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTrainingInstitutionApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 根据培训订单ID和用户ID查询申请记录
     * 
     * @param orderId 培训订单ID
     * @param userId 用户ID
     * @return 申请记录
     */
    public TrainingInstitutionApplication selectApplicationByOrderIdAndUserId(@Param("orderId") Long orderId, @Param("userId") Long userId);

    /**
     * 根据培训订单ID和机构名称查询申请记录
     * 
     * @param orderId 培训订单ID
     * @param institutionName 机构名称
     * @return 申请记录
     */
    public TrainingInstitutionApplication selectApplicationByOrderIdAndInstitutionName(@Param("orderId") Long orderId, @Param("institutionName") String institutionName);

    /**
     * 根据培训订单ID和联系电话查询申请记录
     * 
     * @param orderId 培训订单ID
     * @param contactPhone 联系电话
     * @return 申请记录
     */
    public TrainingInstitutionApplication selectApplicationByOrderIdAndPhone(@Param("orderId") Long orderId, @Param("contactPhone") String contactPhone);

    /**
     * 统计某个培训订单的机构申请数量
     * 
     * @param orderId 培训订单ID
     * @return 申请数量
     */
    public int countApplicationsByOrderId(Long orderId);

    /**
     * 统计某个培训订单的已通过机构申请数量
     * 
     * @param orderId 培训订单ID
     * @return 已通过申请数量
     */
    public int countApprovedApplicationsByOrderId(Long orderId);

    /**
     * 获取某个培训订单的机构申请列表
     * 
     * @param orderId 培训订单ID
     * @return 申请列表
     */
    public List<TrainingInstitutionApplication> getApplicationsByOrderId(Long orderId);

    /**
     * 根据申请状态统计数量
     * 
     * @param status 申请状态
     * @return 数量
     */
    public int countByStatus(String status);

    /**
     * 获取待审核的申请列表
     * 
     * @return 待审核申请列表
     */
    public List<TrainingInstitutionApplication> selectPendingApplications();

    /**
     * 批量更新申请状态
     * 
     * @param applicationIds 申请ID数组
     * @param status 新状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 更新数量
     */
    public int batchUpdateStatus(@Param("applicationIds") Long[] applicationIds, 
                                @Param("status") String status, 
                                @Param("reviewer") String reviewer, 
                                @Param("reviewComment") String reviewComment);
}
