package com.sux.web.controller.job;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.JobPosting;
import com.sux.system.service.IJobPostingService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 招聘信息Controller（核心匹配优化版）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/job/posting")
public class JobPostingController extends BaseController {
    @Autowired
    private IJobPostingService jobPostingService;

    /**
     * 查询招聘信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(JobPosting jobPosting) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingList(jobPosting);
        return getDataTable(list);
    }

    /**
     * 查询我发布的招聘信息列表
     */
    @GetMapping("/my-list")
    public TableDataInfo myList(JobPosting jobPosting) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<JobPosting> list = jobPostingService.selectMyJobPostingList(jobPosting, userId);
        return getDataTable(list);
    }

    /**
     * 查询已发布的招聘信息列表（公开接口）
     */
    @GetMapping("/published")
    public TableDataInfo publishedList(JobPosting jobPosting) {
        startPage();
        List<JobPosting> list = jobPostingService.selectPublishedJobPostingList(jobPosting);
        return getDataTable(list);
    }

    /**
     * 查询热门招聘信息
     */
    @GetMapping("/hot")
    public AjaxResult hotList(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectHotJobPostingList(limit);
        return success(list);
    }

    /**
     * 查询推荐招聘信息
     */
    @GetMapping("/featured")
    public AjaxResult featuredList(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectFeaturedJobPostingList(limit);
        return success(list);
    }

    /**
     * 查询紧急招聘信息
     */
    @GetMapping("/urgent")
    public AjaxResult urgentList(@RequestParam(defaultValue = "10") Integer limit) {
        List<JobPosting> list = jobPostingService.selectUrgentJobPostingList(limit);
        return success(list);
    }

    /**
     * 根据关键词搜索招聘信息
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam String keyword) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByKeyword(keyword);
        return getDataTable(list);
    }

    // ==================== 核心匹配优化接口 ====================

    /**
     * 基于核心字段搜索招聘信息
     */
    @GetMapping("/search-by-core")
    public TableDataInfo searchByCore(@RequestParam(required = false) String jobType,
                                     @RequestParam(required = false) String jobCategory,
                                     @RequestParam(required = false) String salaryType,
                                     @RequestParam(required = false) String educationRequired,
                                     @RequestParam(required = false) String keyword) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByCoreFields(
            jobType, jobCategory, salaryType, educationRequired, keyword);
        return getDataTable(list);
    }

    /**
     * 根据工作类型查询招聘信息
     */
    @GetMapping("/by-job-type")
    public TableDataInfo getByJobType(@RequestParam String jobType) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByJobType(jobType);
        return getDataTable(list);
    }

    /**
     * 根据工作类别查询招聘信息
     */
    @GetMapping("/by-job-category")
    public TableDataInfo getByJobCategory(@RequestParam String jobCategory) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByJobCategory(jobCategory);
        return getDataTable(list);
    }

    /**
     * 根据薪资类型查询招聘信息
     */
    @GetMapping("/by-salary-type")
    public TableDataInfo getBySalaryType(@RequestParam String salaryType) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingBySalaryType(salaryType);
        return getDataTable(list);
    }

    /**
     * 根据学历要求查询招聘信息
     */
    @GetMapping("/by-education")
    public TableDataInfo getByEducation(@RequestParam String educationRequired) {
        startPage();
        List<JobPosting> list = jobPostingService.selectJobPostingByEducation(educationRequired);
        return getDataTable(list);
    }

    /**
     * 查询相似的招聘信息
     */
    @GetMapping("/similar/{jobId}")
    public AjaxResult similarList(@PathVariable Long jobId, @RequestParam(defaultValue = "5") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }
        List<JobPosting> list = jobPostingService.selectSimilarJobPostingList(jobPosting, limit);
        return success(list);
    }

    /**
     * 智能推荐招聘信息
     */
    @GetMapping("/recommend")
    public AjaxResult recommendList(@RequestParam(defaultValue = "10") Integer limit) {
        Long userId = SecurityUtils.getUserId();
        List<JobPosting> list = jobPostingService.recommendJobPostingForUser(userId, limit);
        return success(list);
    }

    /**
     * 导出招聘信息列表
     */
    @Log(title = "招聘信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JobPosting jobPosting) {
        List<JobPosting> list = jobPostingService.selectJobPostingList(jobPosting);
        ExcelUtil<JobPosting> util = new ExcelUtil<JobPosting>(JobPosting.class);
        util.exportExcel(response, list, "招聘信息数据");
    }

    /**
     * 获取招聘信息详细信息
     */
    @GetMapping(value = "/{jobId}")
    public AjaxResult getInfo(@PathVariable("jobId") Long jobId) {
        // 增加浏览次数
        jobPostingService.increaseViewCount(jobId);

        JobPosting jobPosting = jobPostingService.selectJobPostingDetailByJobId(jobId);
        return success(jobPosting);
    }

    /**
     * 新增招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody JobPosting jobPosting) {
        if (!jobPostingService.checkJobTitleUnique(jobPosting)) {
            return error("新增招聘信息'" + jobPosting.getJobTitle() + "'失败，职位名称已存在");
        }
        return toAjax(jobPostingService.insertJobPosting(jobPosting));
    }

    /**
     * 修改招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody JobPosting jobPosting) {
        Long userId = SecurityUtils.getUserId();
        if (!jobPostingService.checkJobPostingEditable(jobPosting.getJobId(), userId)) {
            return error("修改招聘信息失败，无权限或状态不允许修改");
        }
        if (!jobPostingService.checkJobTitleUnique(jobPosting)) {
            return error("修改招聘信息'" + jobPosting.getJobTitle() + "'失败，职位名称已存在");
        }
        return toAjax(jobPostingService.updateJobPosting(jobPosting));
    }

    /**
     * 发布招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{jobId}")
    public AjaxResult publish(@PathVariable Long jobId) {
        Long userId = SecurityUtils.getUserId();
        if (!jobPostingService.checkJobPostingEditable(jobId, userId)) {
            return error("发布招聘信息失败，无权限或状态不允许发布");
        }
        return toAjax(jobPostingService.publishJobPosting(jobId));
    }

    /**
     * 暂停招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.UPDATE)
    @PutMapping("/pause/{jobId}")
    public AjaxResult pause(@PathVariable Long jobId) {
        Long userId = SecurityUtils.getUserId();
        if (!jobPostingService.checkJobPostingEditable(jobId, userId)) {
            return error("暂停招聘信息失败，无权限或状态不允许暂停");
        }
        return toAjax(jobPostingService.pauseJobPosting(jobId));
    }

    /**
     * 关闭招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.UPDATE)
    @PutMapping("/close/{jobId}")
    public AjaxResult close(@PathVariable Long jobId) {
        Long userId = SecurityUtils.getUserId();
        if (!jobPostingService.checkJobPostingEditable(jobId, userId)) {
            return error("关闭招聘信息失败，无权限或状态不允许关闭");
        }
        return toAjax(jobPostingService.closeJobPosting(jobId));
    }

    /**
     * 完成招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{jobId}")
    public AjaxResult complete(@PathVariable Long jobId) {
        Long userId = SecurityUtils.getUserId();
        if (!jobPostingService.checkJobPostingEditable(jobId, userId)) {
            return error("完成招聘信息失败，无权限或状态不允许完成");
        }
        return toAjax(jobPostingService.completeJobPosting(jobId));
    }

    /**
     * 删除招聘信息
     */
    @Log(title = "招聘信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobIds}")
    public AjaxResult remove(@PathVariable Long[] jobIds) {
        Long userId = SecurityUtils.getUserId();
        for (Long jobId : jobIds) {
            if (!jobPostingService.checkJobPostingDeletable(jobId, userId)) {
                return error("删除招聘信息失败，无权限或状态不允许删除");
            }
        }
        return toAjax(jobPostingService.deleteJobPostingByJobIds(jobIds));
    }

    /**
     * 批量更新招聘信息状态
     */
    @Log(title = "招聘信息", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-status")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        Long[] jobIds = (Long[]) params.get("jobIds");
        String status = (String) params.get("status");
        return toAjax(jobPostingService.batchUpdateJobPostingStatus(jobIds, status));
    }

    /**
     * 查询招聘信息统计数据
     */
    @GetMapping("/statistics")
    public AjaxResult statistics(@RequestParam(required = false) Long publisherUserId) {
        Map<String, Object> data = jobPostingService.selectJobPostingStatistics(publisherUserId);
        return success(data);
    }

    /**
     * 根据工作类别统计招聘信息数量
     */
    @GetMapping("/statistics/category")
    public AjaxResult statisticsByCategory() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountByCategory();
        return success(data);
    }

    /**
     * 根据工作地点统计招聘信息数量
     */
    @GetMapping("/statistics/location")
    public AjaxResult statisticsByLocation() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountByLocation();
        return success(data);
    }

    /**
     * 根据薪资范围统计招聘信息数量
     */
    @GetMapping("/statistics/salary")
    public AjaxResult statisticsBySalary() {
        List<Map<String, Object>> data = jobPostingService.selectJobPostingCountBySalaryRange();
        return success(data);
    }

    /**
     * 查询即将截止的招聘信息
     */
    @GetMapping("/expiring")
    public AjaxResult expiringList(@RequestParam(defaultValue = "7") Integer days) {
        List<JobPosting> list = jobPostingService.selectJobPostingExpiringSoon(days);
        return success(list);
    }

    /**
     * 根据招聘信息匹配零工（带相似度评分）
     */
    @GetMapping("/match-workers/{jobId}")
    public AjaxResult matchWorkers(@PathVariable Long jobId, @RequestParam(defaultValue = "10") Integer limit) {
        JobPosting jobPosting = jobPostingService.selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return error("招聘信息不存在");
        }

        List<Map<String, Object>> matchResults = jobPostingService.matchWorkersWithSimilarity(jobId, limit);
        return success(matchResults);
    }

    /**
     * 计算招聘信息与零工的相似度
     */
    @GetMapping("/similarity/{jobId}/{workerId}")
    public AjaxResult calculateSimilarity(@PathVariable Long jobId, @PathVariable Long workerId) {
        Double similarity = jobPostingService.calculateJobWorkerSimilarity(jobId, workerId);
        if (similarity == null) {
            return error("计算相似度失败，请检查招聘信息或零工信息是否存在");
        }

        return success()
                .put("jobId", jobId)
                .put("workerId", workerId)
                .put("similarity", similarity)
                .put("similarityPercentage", Math.round(similarity * 100));
    }

    // ==================== 选项列表接口 ====================

    /**
     * 获取所有工作类型列表
     */
    @GetMapping("/options/job-types")
    public AjaxResult getJobTypeOptions() {
        List<String> jobTypes = jobPostingService.selectAllJobTypes();
        return success(jobTypes);
    }

    /**
     * 获取所有工作类别列表
     */
    @GetMapping("/options/job-categories")
    public AjaxResult getJobCategoryOptions() {
        List<String> jobCategories = jobPostingService.selectAllJobCategories();
        return success(jobCategories);
    }

    /**
     * 获取所有薪资类型列表
     */
    @GetMapping("/options/salary-types")
    public AjaxResult getSalaryTypeOptions() {
        List<String> salaryTypes = jobPostingService.selectAllSalaryTypes();
        return success(salaryTypes);
    }

    /**
     * 获取所有学历要求列表
     */
    @GetMapping("/options/education-requirements")
    public AjaxResult getEducationRequirementOptions() {
        List<String> educationRequirements = jobPostingService.selectAllEducationRequirements();
        return success(educationRequirements);
    }
}
