<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.PlaceInfoMapper">

    <resultMap type="PlaceInfo" id="PlaceInfoResult">
        <result property="placeId"                  column="place_id"                  />
        <result property="placeName"                column="place_name"                />
        <result property="placeCode"                column="place_code"                />
        <result property="placeType"                column="place_type"                />
        <result property="placeLevel"               column="place_level"               />
        <result property="placeArea"                column="place_area"                />
        <result property="usableArea"               column="usable_area"               />
        <result property="address"                  column="address"                   />
        <result property="regionCode"               column="region_code"               />
        <result property="regionName"               column="region_name"               />
        <result property="longitude"                column="longitude"                 />
        <result property="latitude"                 column="latitude"                  />
        <result property="contactPerson"            column="contact_person"            />
        <result property="contactPhone"             column="contact_phone"             />
        <result property="contactEmail"             column="contact_email"             />
        <result property="companyCount"             column="company_count"             />
        <result property="availablePositions"       column="available_positions"       />
        <result property="occupiedPositions"        column="occupied_positions"        />
        <result property="rentPriceMin"             column="rent_price_min"            />
        <result property="rentPriceMax"             column="rent_price_max"            />
        <result property="operationMode"            column="operation_mode"            />
        <result property="industryDirection"        column="industry_direction"        />
        <result property="serviceFacilities"        column="service_facilities"        />
        <result property="preferentialPolicies"     column="preferential_policies"     />
        <result property="applyStartDate"           column="apply_start_date"          />
        <result property="applyEndDate"             column="apply_end_date"            />
        <result property="applyTimeStatus"          column="apply_time_status"         />
        <result property="isOpenSettle"             column="is_open_settle"            />
        <result property="imageUrl"                 column="image_url"                 />
        <result property="imageGallery"             column="image_gallery"             />
        <result property="description"              column="description"               />
        <result property="noticeDetail"             column="notice_detail"             />
        <result property="status"                   column="status"                    />
        <result property="isFeatured"               column="is_featured"               />
        <result property="sortOrder"                column="sort_order"                />
        <result property="viewCount"                column="view_count"                />
        <result property="createId"                 column="create_id"                 />
        <result property="createTime"               column="create_time"               />
        <result property="updateId"                 column="update_id"                 />
        <result property="updateTime"               column="update_time"               />
        <result property="delFlag"                  column="del_flag"                  />
        <result property="remark"                   column="remark"                    />
    </resultMap>

    <sql id="selectPlaceInfoVo">
        select place_id, place_name, place_code, place_type, place_level, place_area, usable_area,
               address, region_code, region_name, longitude, latitude, contact_person, contact_phone, contact_email,
               company_count, available_positions, occupied_positions, rent_price_min, rent_price_max,
               operation_mode, industry_direction, service_facilities, preferential_policies,
               apply_start_date, apply_end_date, apply_time_status, is_open_settle,
               image_url, image_gallery, description, notice_detail, status, is_featured, sort_order, view_count,
               create_id, create_time, update_id, update_time, del_flag, remark
        from place_info
    </sql>

    <select id="selectPlaceInfoList" parameterType="PlaceInfo" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        <where>
            del_flag = '0'
            <if test="placeName != null and placeName != ''"> and place_name like concat('%', #{placeName}, '%')</if>
            <if test="placeCode != null and placeCode != ''"> and place_code = #{placeCode}</if>
            <if test="placeType != null and placeType != ''"> and place_type = #{placeType}</if>
            <if test="placeLevel != null and placeLevel != ''"> and place_level = #{placeLevel}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
            <if test="regionName != null and regionName != ''"> and region_name like concat('%', #{regionName}, '%')</if>
            <if test="operationMode != null and operationMode != ''"> and operation_mode = #{operationMode}</if>
            <if test="industryDirection != null and industryDirection != ''"> and industry_direction like concat('%', #{industryDirection}, '%')</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="isFeatured != null"> and is_featured = #{isFeatured}</if>
            <if test="isOpenSettle != null"> and is_open_settle = #{isOpenSettle}</if>
        </where>
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoByPlaceId" parameterType="Long" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where place_id = #{placeId} and del_flag = '0'
    </select>

    <insert id="insertPlaceInfo" parameterType="PlaceInfo" useGeneratedKeys="true" keyProperty="placeId">
        insert into place_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="placeName != null and placeName != ''">place_name,</if>
            <if test="placeCode != null and placeCode != ''">place_code,</if>
            <if test="placeType != null and placeType != ''">place_type,</if>
            <if test="placeLevel != null and placeLevel != ''">place_level,</if>
            <if test="placeArea != null">place_area,</if>
            <if test="usableArea != null">usable_area,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="regionCode != null and regionCode != ''">region_code,</if>
            <if test="regionName != null and regionName != ''">region_name,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email,</if>
            <if test="companyCount != null">company_count,</if>
            <if test="availablePositions != null">available_positions,</if>
            <if test="occupiedPositions != null">occupied_positions,</if>
            <if test="rentPriceMin != null">rent_price_min,</if>
            <if test="rentPriceMax != null">rent_price_max,</if>
            <if test="operationMode != null and operationMode != ''">operation_mode,</if>
            <if test="industryDirection != null and industryDirection != ''">industry_direction,</if>
            <if test="serviceFacilities != null">service_facilities,</if>
            <if test="preferentialPolicies != null">preferential_policies,</if>
            <if test="applyStartDate != null">apply_start_date,</if>
            <if test="applyEndDate != null">apply_end_date,</if>
            <if test="applyTimeStatus != null">apply_time_status,</if>
            <if test="isOpenSettle != null">is_open_settle,</if>
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="imageGallery != null">image_gallery,</if>
            <if test="description != null">description,</if>
            <if test="noticeDetail != null">notice_detail,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isFeatured != null">is_featured,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="placeName != null and placeName != ''">#{placeName},</if>
            <if test="placeCode != null and placeCode != ''">#{placeCode},</if>
            <if test="placeType != null and placeType != ''">#{placeType},</if>
            <if test="placeLevel != null and placeLevel != ''">#{placeLevel},</if>
            <if test="placeArea != null">#{placeArea},</if>
            <if test="usableArea != null">#{usableArea},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="regionCode != null and regionCode != ''">#{regionCode},</if>
            <if test="regionName != null and regionName != ''">#{regionName},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">#{contactEmail},</if>
            <if test="companyCount != null">#{companyCount},</if>
            <if test="availablePositions != null">#{availablePositions},</if>
            <if test="occupiedPositions != null">#{occupiedPositions},</if>
            <if test="rentPriceMin != null">#{rentPriceMin},</if>
            <if test="rentPriceMax != null">#{rentPriceMax},</if>
            <if test="operationMode != null and operationMode != ''">#{operationMode},</if>
            <if test="industryDirection != null and industryDirection != ''">#{industryDirection},</if>
            <if test="serviceFacilities != null">#{serviceFacilities},</if>
            <if test="preferentialPolicies != null">#{preferentialPolicies},</if>
            <if test="applyStartDate != null">#{applyStartDate},</if>
            <if test="applyEndDate != null">#{applyEndDate},</if>
            <if test="applyTimeStatus != null">#{applyTimeStatus},</if>
            <if test="isOpenSettle != null">#{isOpenSettle},</if>
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="imageGallery != null">#{imageGallery},</if>
            <if test="description != null">#{description},</if>
            <if test="noticeDetail != null">#{noticeDetail},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isFeatured != null">#{isFeatured},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePlaceInfo" parameterType="PlaceInfo">
        update place_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="placeName != null and placeName != ''">place_name = #{placeName},</if>
            <if test="placeCode != null and placeCode != ''">place_code = #{placeCode},</if>
            <if test="placeType != null and placeType != ''">place_type = #{placeType},</if>
            <if test="placeLevel != null and placeLevel != ''">place_level = #{placeLevel},</if>
            <if test="placeArea != null">place_area = #{placeArea},</if>
            <if test="usableArea != null">usable_area = #{usableArea},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="regionCode != null and regionCode != ''">region_code = #{regionCode},</if>
            <if test="regionName != null and regionName != ''">region_name = #{regionName},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="companyCount != null">company_count = #{companyCount},</if>
            <if test="availablePositions != null">available_positions = #{availablePositions},</if>
            <if test="occupiedPositions != null">occupied_positions = #{occupiedPositions},</if>
            <if test="rentPriceMin != null">rent_price_min = #{rentPriceMin},</if>
            <if test="rentPriceMax != null">rent_price_max = #{rentPriceMax},</if>
            <if test="operationMode != null and operationMode != ''">operation_mode = #{operationMode},</if>
            <if test="industryDirection != null and industryDirection != ''">industry_direction = #{industryDirection},</if>
            <if test="serviceFacilities != null">service_facilities = #{serviceFacilities},</if>
            <if test="preferentialPolicies != null">preferential_policies = #{preferentialPolicies},</if>
            <if test="applyStartDate != null">apply_start_date = #{applyStartDate},</if>
            <if test="applyEndDate != null">apply_end_date = #{applyEndDate},</if>
            <if test="applyTimeStatus != null">apply_time_status = #{applyTimeStatus},</if>
            <if test="isOpenSettle != null">is_open_settle = #{isOpenSettle},</if>
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="imageGallery != null">image_gallery = #{imageGallery},</if>
            <if test="description != null">description = #{description},</if>
            <if test="noticeDetail != null">notice_detail = #{noticeDetail},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isFeatured != null">is_featured = #{isFeatured},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where place_id = #{placeId}
    </update>

    <delete id="deletePlaceInfoByPlaceId" parameterType="Long">
        update place_info set del_flag = '2' where place_id = #{placeId}
    </delete>

    <delete id="deletePlaceInfoByPlaceIds" parameterType="String">
        update place_info set del_flag = '2' where place_id in
        <foreach item="placeId" collection="array" open="(" separator="," close=")">
            #{placeId}
        </foreach>
    </delete>

    <select id="selectFeaturedPlaceInfoList" parameterType="PlaceInfo" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        <where>
            del_flag = '0' and status = '0' and is_featured = 1
            <if test="placeType != null and placeType != ''"> and place_type = #{placeType}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
        </where>
        order by sort_order asc, create_time desc
    </select>

    <select id="selectActivePlaceInfoList" parameterType="PlaceInfo" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        <where>
            del_flag = '0' and status = '0' and is_open_settle = 1
            <if test="placeType != null and placeType != ''"> and place_type = #{placeType}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
        </where>
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoByType" parameterType="String" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0' and place_type = #{placeType}
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoByRegion" parameterType="String" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0' and region_code = #{regionCode}
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoByLevel" parameterType="String" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0' and place_level = #{placeLevel}
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoByIndustry" parameterType="String" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0' and industry_direction like concat('%', #{industryDirection}, '%')
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoByOperationMode" parameterType="String" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0' and operation_mode = #{operationMode}
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <select id="selectPlaceInfoStatistics" resultType="Map">
        select
            count(*) as total_count,
            sum(case when status = '0' then 1 else 0 end) as active_count,
            sum(case when is_featured = 1 then 1 else 0 end) as featured_count,
            sum(case when is_open_settle = 1 then 1 else 0 end) as open_settle_count,
            sum(company_count) as total_company_count,
            sum(available_positions) as total_available_positions,
            sum(occupied_positions) as total_occupied_positions,
            sum(place_area) as total_place_area,
            sum(usable_area) as total_usable_area
        from place_info
        where del_flag = '0'
    </select>

    <select id="selectPlaceInfoByKeyword" parameterType="String" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0'
        and (place_name like concat('%', #{keyword}, '%')
             or address like concat('%', #{keyword}, '%')
             or industry_direction like concat('%', #{keyword}, '%')
             or description like concat('%', #{keyword}, '%'))
        order by is_featured desc, sort_order asc, create_time desc
    </select>

    <update id="updatePlaceInfoViewCount" parameterType="Long">
        update place_info set view_count = view_count + 1 where place_id = #{placeId}
    </update>

    <select id="selectPlaceInfoExpiringSoon" parameterType="Integer" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where del_flag = '0' and status = '0' and apply_time_status = 1
        and apply_end_date is not null
        and apply_end_date between now() and date_add(now(), interval #{days} day)
        order by apply_end_date asc
    </select>

    <select id="selectPlaceInfoDetailByPlaceId" parameterType="Long" resultMap="PlaceInfoResult">
        <include refid="selectPlaceInfoVo"/>
        where place_id = #{placeId} and del_flag = '0'
    </select>

    <select id="selectAllPlaceTypes" resultType="String">
        select distinct place_type from place_info
        where del_flag = '0' and place_type is not null and place_type != ''
        order by place_type
    </select>

    <select id="selectAllPlaceLevels" resultType="String">
        select distinct place_level from place_info
        where del_flag = '0' and place_level is not null and place_level != ''
        order by place_level
    </select>

    <select id="selectAllRegions" resultType="Map">
        select distinct region_code as regionCode, region_name as regionName from place_info
        where del_flag = '0' and region_code is not null and region_code != ''
        order by region_code
    </select>

    <select id="selectAllIndustryDirections" resultType="String">
        select distinct industry_direction from place_info
        where del_flag = '0' and industry_direction is not null and industry_direction != ''
        order by industry_direction
    </select>

    <select id="selectAllOperationModes" resultType="String">
        select distinct operation_mode from place_info
        where del_flag = '0' and operation_mode is not null and operation_mode != ''
        order by operation_mode
    </select>

</mapper>
