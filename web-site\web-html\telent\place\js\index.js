// 公用模块html
headerBar()
footerBar()

$("#placePage").addClass("on")
function clearsx(){
    localStorage.removeItem('cdposition');
    localStorage.removeItem('cdtype');
    localStorage.removeItem('cdarea');
    localStorage.removeItem('cddirection');
    localStorage.removeItem('cdmoney');
    localStorage.removeItem('cdlevel');
}
clearsx()

// API基础配置
const API_BASE_URL = '/api';

// 场地信息API调用函数
const placeAPI = {
    // 获取场地列表
    getPlaceList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/active`,
            method: 'GET',
            data: params
        });
    },

    // 获取推荐场地列表
    getFeaturedPlaceList: function(params = {}) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/featured`,
            method: 'GET',
            data: params
        });
    },

    // 获取场地统计信息
    getPlaceStatistics: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/statistics`,
            method: 'GET'
        });
    },

    // 获取所有场地类型
    getAllPlaceTypes: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/types`,
            method: 'GET'
        });
    },

    // 获取所有场地等级
    getAllPlaceLevels: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/levels`,
            method: 'GET'
        });
    },

    // 获取所有区域
    getAllRegions: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/regions`,
            method: 'GET'
        });
    },

    // 获取所有行业方向
    getAllIndustryDirections: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/industries`,
            method: 'GET'
        });
    },

    // 获取所有运营模式
    getAllOperationModes: function() {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/operations`,
            method: 'GET'
        });
    },

    // 根据关键词搜索场地
    searchPlaces: function(keyword) {
        return $.ajax({
            url: `${API_BASE_URL}/place/info/search`,
            method: 'GET',
            data: { keyword: keyword }
        });
    }
};
var viewModel = {
    cdggList: ko.observableArray(),//公告列表
    cycdList: ko.observableArray(),
    cdxqCount: ko.observable(),//场地需求总数
    cdxqList: ko.observableArray(),//场地需求总数
    bagnnerList: ko.observableArray(),
    position: ko.observable(''),//场地位置：
        positionName: ko.observable('全部'),//场地位置：
        positionList: ko.observableArray(), //场地位置：

        type: ko.observable(''),//场地类型：
        typeName: ko.observable('全部'),//场地类型：
        typeList: ko.observableArray(),//场地类型：

        level: ko.observable(''),
        levelName: ko.observable('全部'),
        levelList: ko.observableArray(),
       
        area: ko.observable(''),//场地面积：
        areaName: ko.observable('全部'),//场地面积：
        areaList01: ko.observableArray(),

        money: ko.observable(''),//收费类型：
        moneyName: ko.observable('全部'),//收费类型：
        moneyList: ko.observableArray(),

        direction: ko.observable(''),//行业方向
        directionName: ko.observable('全部'),//行业方向
        directionList: ko.observableArray(),
        selectData: function (data, data2) { //筛选项
            changeSelectStyle(data,data2)
            switch (data) {
                case '0':
                    viewModel.position(data2.baseId)
                    localStorage.setItem('cdposition',data2.baseId)
                    viewModel.positionName(data2.baseName)
                    break;
                case '1':
                    viewModel.type(data2.baseId)
                    localStorage.setItem('cdtype',data2.baseId)
                    viewModel.typeName(data2.baseName)
                    initarea()
                    viewModel.area('')
                    viewModel.areaName('全部')
                    $('.module3').removeClass('on')
                    break;
            
                case '3':
                    viewModel.level(data2.baseId)
                    localStorage.setItem('cdlevel',data2.baseId)
                    viewModel.levelName(data2.baseName)
                    break;
                case '2':
                    viewModel.area(data2.baseId)
                    localStorage.setItem('cdarea',data2.baseId)
                    viewModel.areaName(data2.baseName)
                    break;
                case '4':
                    viewModel.direction(data2.baseId)
                    localStorage.setItem('cddirection',data2.baseId)
                    viewModel.directionName(data2.baseName)
                    break;
                case '5':
                    viewModel.money(data2.baseId)
                    localStorage.setItem('cdmoney',data2.baseId)
                    viewModel.moneyName(data2.baseName)
                    break;
            }
    
            getCycdList(1)
        },
        parkNoticeList:ko.observableArray(),
        qyzsList:ko.observableArray(),//园区招商
        yqzsInfo:function(data){
            window.open('./attractDet.html?id='+data.baseId)
        }
}
Object.assign(viewModel, viewModel1);
// 更多列表
function moreList(e){
    if(viewModel.cycdList().length>0){
        window.location.href='./placeList.html?index=1'
    }else{
        clearsx()
        window.location.href='./placeList.html'
    }
}
function changeSelectStyle(index,data){
    $('.selectModule').eq(index).addClass('on');

    if(!data.baseId){
        $('.selectModule').eq(index).removeClass('on');
    }
}
$('.selectList').mouseleave(function(){
    $(this).hide()
})
$('.selectModule').click(function(){
    if($(this).hasClass('module3')){
        if(!viewModel.type()){
            $.jBox.tip("请先选择场地类型！");
            return false
        }
    }
    $(this).siblings().show();
    for(var i = 0;i<$(this).parent().siblings().children().length;i++){
        if(i%2==1){
            $(this).parent().siblings().children().eq(i).hide();
        }
    }
})

// 初始化所有筛选选项数据
function initAllFilterOptions() {
    // 初始化区域列表
    placeAPI.getAllRegions().done(function(response) {
        if (response.code === 200) {
            var regions = response.data.map(function(item) {
                return {
                    baseName: item.regionName,
                    baseId: item.regionCode
                };
            });
            regions.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.positionList(regions);
        }
    });

    // 初始化场地类型
    placeAPI.getAllPlaceTypes().done(function(response) {
        if (response.code === 200) {
            var types = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            types.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.typeList(types);
        }
    });

    // 初始化场地等级
    placeAPI.getAllPlaceLevels().done(function(response) {
        if (response.code === 200) {
            var levels = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            levels.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.levelList(levels);
        }
    });

    // 初始化行业方向
    placeAPI.getAllIndustryDirections().done(function(response) {
        if (response.code === 200) {
            var directions = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            directions.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.directionList(directions);
        }
    });

    // 初始化运营模式（作为收费类型的替代）
    placeAPI.getAllOperationModes().done(function(response) {
        if (response.code === 200) {
            var modes = response.data.map(function(item) {
                return {
                    baseName: item,
                    baseId: item
                };
            });
            modes.unshift({
                baseName: '全部',
                baseId: ''
            });
            viewModel.moneyList(modes);
        }
    });
}

// 初始化场地面积选项（保持原有逻辑）
function initarea() {
    // 这里可以根据场地类型动态加载面积范围
    var areaOptions = [
        { baseName: '全部', baseId: '' },
        { baseName: '1000平方米以下', baseId: '0-1000' },
        { baseName: '1000-5000平方米', baseId: '1000-5000' },
        { baseName: '5000-10000平方米', baseId: '5000-10000' },
        { baseName: '10000平方米以上', baseId: '10000-999999' }
    ];
    viewModel.areaList01(areaOptions);
}
// 初始化 场地面积
function initarea() {
ajaxgetDataFull('api-app/v1/sysDictData/selectListByTypeid?typeId='+viewModel.type())
if (getData.code == 0) {
    var arr = getData.obj
    arr.unshift({
        baseName: '全部',
        baseId: ''
    })
    viewModel.areaList01(arr);
}
}
// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:2
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./image/index_banner.png'})
        }
    }
}
// initBanner()
function linkPage(e){
    if(e==1){
        window.location.href='placeList.html'
    }else{
        window.location.href='demandList.html'
    }
}
// 场地公告
// getCdGg();
// function getCdGg() {
//     ajaxgetData('api-app/v1/qctParkNotice/top10', '', function (res) {
//         if (res.code == 0) {
//             res.obj.forEach(function (ele) {
//                 var date = ele.baseCreateTime
//                 ele.baseCreateTime = date.slice(0, 7);
//                 ele.date = date.slice(8, 10);
//                 ele.parkNoticeDetail = delHtmlTag(ele.parkNoticeDetail);
//             });
//             viewModel.cdggList(res.obj)
//             $(".slideTxtBox").slide({ mainCell: ".bd ul", autoPlay: true, effect: "left", interTime: 5000 });
//         }
//     })
// }
// 创业场地
getCycdList();
function getCycdList() {
    var params = {
        pageSize: 6,
        pageNum: 1
    };

    // 添加筛选条件
    if (viewModel.level()) {
        params.placeLevel = viewModel.level();
    }
    if (viewModel.position()) {
        params.regionCode = viewModel.position();
    }
    if (viewModel.type()) {
        params.placeType = viewModel.type();
    }
    if (viewModel.direction()) {
        params.industryDirection = viewModel.direction();
    }
    if (viewModel.money()) {
        params.operationMode = viewModel.money();
    }

    placeAPI.getPlaceList(params).done(function(response) {
        if (response.code === 200) {
            var places = response.rows || [];
            if (places.length > 0) {
                $(".nodataPicCycd").hide(); // 暂无数据-隐藏
            } else {
                $(".nodataPicCycd").show(); // 暂无数据-显示
            }

            // 数据格式转换，适配现有模板
            places.forEach(function(place) {
                place.parkName = place.placeName;
                place.parkAddress = place.address;
                place.parkLevel = place.placeLevel;
                place.parkType = place.placeType;
                place.parkArea = place.placeArea;
                place.usableArea = place.usableArea;
                place.companyCount = place.companyCount;
                place.availablePositions = place.availablePositions;
                place.occupiedPositions = place.occupiedPositions;
                place.rentPriceMin = place.rentPriceMin;
                place.rentPriceMax = place.rentPriceMax;
                place.industryDirection = place.industryDirection;
                place.operationMode = place.operationMode;
                place.contactPerson = place.contactPerson;
                place.contactPhone = place.contactPhone;
                place.description = place.description;
                place.imageUrl = place.imageUrl || './image/place_default.jpg';
            });

            viewModel.cycdList(places);
        }
    }).fail(function() {
        $(".nodataPicCycd").show();
        viewModel.cycdList([]);
    });
}
// 园区动态
getNoticeList();
function getNoticeList() {
    var obj = {
        pageSize: 2,
        pageNum: 1
    };
    ajaxgetData('api-qingdao/v1/qctParkInformation/front/getParkInformationList', obj, function (data) {
        viewModel.parkNoticeList(data.obj.content);
    });
}
// 初始化统计数据
initnum()
function initnum(){
    placeAPI.getPlaceStatistics().done(function(response) {
        if (response.code === 200) {
            var stats = response.data;

            // 场地总数
            $("#onerun01").numberAnimate({
                num: stats.total_count || 0,
                speed: 2000
            });

            // 总面积（平方米）
            $("#onerun02").numberAnimate({
                num: Math.round(stats.total_place_area || 0),
                speed: 2000
            });

            // 可使用面积（平方米）
            $("#onerun03").numberAnimate({
                num: Math.round(stats.total_usable_area || 0),
                speed: 2000
            });

            // 入驻企业总数
            $("#onerun04").numberAnimate({
                num: stats.total_company_count || 0,
                speed: 2000
            });
        }
    }).fail(function() {
        // 失败时显示默认值
        $("#onerun01").numberAnimate({ num: 0, speed: 2000 });
        $("#onerun02").numberAnimate({ num: 0, speed: 2000 });
        $("#onerun03").numberAnimate({ num: 0, speed: 2000 });
        $("#onerun04").numberAnimate({ num: 0, speed: 2000 });
    });
}

// 场地需求
getCdxqList();
function getCdxqList() {
    var obj = {
        pageSize: 4,
        pageNum: 1,
        publisher:0
    };
    ajaxgetData('api-qingdao/v1/qctParkRequire/frontend', obj, function (data) {
        // data.obj.content=[]
        if (data.obj.content.length > 0) {
            $(".nodataPicCdxq").hide();//暂无数据-隐藏    
        } else {
            $(".nodataPicCdxq").show();//暂无数据-显示
        }
        viewModel.cdxqCount(data.obj.totalCount);
        data.obj.content.forEach(function (ele) {
            ele.demandIntroduct = delHtmlTag(ele.demandIntroduct);
        });
        viewModel.cdxqList(data.obj.content);
    });
}
// 园区招商需求
getQyzsList();
function getQyzsList() {
    var obj = {
        pageSize: 4,
        pageNum: 1,
        publisher:1
    };
    ajaxgetData('api-qingdao/v1/qctParkRequire/frontend', obj, function (data) {
        if (data.obj.content.length > 0) {
            $(".nodataPicQyzs").hide();//暂无数据-隐藏    
        } else {
            $(".nodataPicQyzs").show();//暂无数据-显示
        }
        viewModel.qyzsList(data.obj.content);
    });
}
// 申请入驻
function sqrzFun() {
    if (!checkLogin()) {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', '../place/index.html');
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    } else {
        if (viewModel.mine().userType ==0) {//个人类型账号
            $.jBox.tip("个人账号无法认证为场地服务商，请通过企业账号进行申请入驻操作！");
        }else{
            if (viewModel.mine().fieldService == '0' || viewModel.mine().fieldService == '2' || viewModel.mine().fieldService == '3') {//未认证为场地服务商
                window.open("../member/authSpace.html?type=3");//跳转认证页面
            } else if (viewModel.mine().fieldService == '1') {//已经认证为场地服务商
                $.jBox.tip("您已认证为场地服务商");
            }
        }
        
    }
}
// 发布需求
function fbxqNow() {
    if (!checkLogin()) {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', '../place/index.html');
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    } else {
        if (viewModel.mine().fieldService == '1') {
            $.jBox.tip("场地服务商无法发布场地需求！")
            return false;
        }
        window.open("../member/parkDemandList.html");//跳转我发布的场地需求页面
    }
}
ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});
$(".yqdtOut").slide({
    mainCell:"ul",
    autoPlay:true,
    effect:"top",
    interTime:5000,
    autoPage:true,
    vis:2,
    scroll:2
});
